<?php
// 调试版本的API - 显示详细错误信息
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json; charset=utf-8');

// 加载配置
try {
    $config = require_once 'config.php';
    echo json_encode([
        'status' => 'success',
        'message' => '配置加载成功',
        'config_check' => [
            'qwen_api_key' => !empty($config['qwen']['api_key']) && $config['qwen']['api_key'] !== 'YOUR_QWEN_API_KEY_HERE',
            'deepseek_api_key' => !empty($config['deepseek']['api_key']) && $config['deepseek']['api_key'] !== 'YOUR_DEEPSEEK_API_KEY_HERE',
            'qwen_model' => $config['qwen']['model'],
            'deepseek_model' => $config['deepseek']['model'],
            'qwen_temperature' => $config['qwen']['temperature'],
            'deepseek_temperature' => $config['deepseek']['temperature']
        ]
    ], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '配置加载失败: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
