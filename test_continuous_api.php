<?php
// 测试连续API调用功能
echo "=== 连续API调用测试 ===\n\n";

$testImageUrl = "http://solve.igmdns.com/img/01.jpg";
$testCount = 3;
$intervalSeconds = 1;

echo "测试配置:\n";
echo "- 图片URL: $testImageUrl\n";
echo "- 测试次数: $testCount\n";
echo "- 间隔时间: {$intervalSeconds}秒\n\n";

$results = [];
$successCount = 0;
$errorCount = 0;
$totalResponseTime = 0;
$totalTokens = 0;

for ($i = 1; $i <= $testCount; $i++) {
    echo "=== 第 $i 次测试 ===\n";
    
    $startTime = microtime(true);
    
    // 构建请求数据
    $requestData = ['image_url' => $testImageUrl];
    
    // 发送API请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8080/index.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 120);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    
    $endTime = microtime(true);
    $responseTime = ($endTime - $startTime) * 1000; // 毫秒
    
    $result = [
        'test_number' => $i,
        'response_time' => $responseTime,
        'http_code' => $httpCode,
        'curl_error' => $curlError,
        'success' => false,
        'data' => null,
        'error' => null
    ];
    
    if ($curlError) {
        $result['error'] = "CURL错误: $curlError";
        echo "❌ CURL错误: $curlError\n";
        $errorCount++;
    } elseif ($httpCode !== 200) {
        $result['error'] = "HTTP错误: $httpCode";
        echo "❌ HTTP错误: $httpCode\n";
        $errorCount++;
    } else {
        $responseData = json_decode($response, true);
        
        if ($responseData === null) {
            $result['error'] = "JSON解析错误";
            echo "❌ JSON解析错误\n";
            $errorCount++;
        } elseif (isset($responseData['success']) && $responseData['success']) {
            $result['success'] = true;
            $result['data'] = $responseData;
            $successCount++;
            $totalResponseTime += $responseTime;
            
            echo "✅ 测试成功\n";
            echo "响应时间: " . number_format($responseTime, 2) . "ms\n";
            
            // 统计Token使用
            $testTokens = 0;
            if (isset($responseData['qwen_response']['tokens'])) {
                $qwenTokens = $responseData['qwen_response']['tokens']['total_tokens'];
                $testTokens += $qwenTokens;
                echo "Qwen Token: $qwenTokens\n";
            }
            
            if (isset($responseData['deepseek_response']['tokens'])) {
                $deepseekTokens = $responseData['deepseek_response']['tokens']['total_tokens'];
                $testTokens += $deepseekTokens;
                echo "DeepSeek Token: $deepseekTokens\n";
            }
            
            $totalTokens += $testTokens;
            echo "本次Token总计: $testTokens\n";
            
        } else {
            $result['error'] = $responseData['error'] ?? '未知API错误';
            echo "❌ API错误: " . $result['error'] . "\n";
            $errorCount++;
        }
    }
    
    $results[] = $result;
    
    echo "状态: " . ($result['success'] ? '成功' : '失败') . "\n";
    echo "耗时: " . number_format($responseTime, 2) . "ms\n\n";
    
    // 等待间隔时间（除了最后一次）
    if ($i < $testCount && $intervalSeconds > 0) {
        echo "等待 {$intervalSeconds} 秒...\n\n";
        sleep($intervalSeconds);
    }
}

// 生成统计报告
echo "=== 📊 测试统计报告 ===\n";
echo "总测试次数: $testCount\n";
echo "成功次数: $successCount\n";
echo "失败次数: $errorCount\n";

$successRate = $testCount > 0 ? ($successCount / $testCount) * 100 : 0;
echo "成功率: " . number_format($successRate, 1) . "%\n";

$avgResponseTime = $successCount > 0 ? $totalResponseTime / $successCount : 0;
echo "平均响应时间: " . number_format($avgResponseTime, 2) . "ms\n";

echo "总Token消耗: $totalTokens\n";

if ($successCount > 0) {
    $responseTimes = array_column(array_filter($results, function($r) { return $r['success']; }), 'response_time');
    $minTime = min($responseTimes);
    $maxTime = max($responseTimes);
    echo "最快响应: " . number_format($minTime, 2) . "ms\n";
    echo "最慢响应: " . number_format($maxTime, 2) . "ms\n";
}

// 详细结果
echo "\n=== 📋 详细测试结果 ===\n";
foreach ($results as $result) {
    echo "第{$result['test_number']}次: ";
    echo ($result['success'] ? '✅ 成功' : '❌ 失败');
    echo " (" . number_format($result['response_time'], 2) . "ms)";
    if (!$result['success']) {
        echo " - " . $result['error'];
    }
    echo "\n";
}

// 性能分析
if ($successCount > 1) {
    echo "\n=== 📈 性能分析 ===\n";
    $responseTimes = array_column(array_filter($results, function($r) { return $r['success']; }), 'response_time');
    
    // 计算标准差
    $mean = array_sum($responseTimes) / count($responseTimes);
    $variance = array_sum(array_map(function($x) use ($mean) { return pow($x - $mean, 2); }, $responseTimes)) / count($responseTimes);
    $stdDev = sqrt($variance);
    
    echo "响应时间标准差: " . number_format($stdDev, 2) . "ms\n";
    echo "性能稳定性: " . ($stdDev < 1000 ? '良好' : ($stdDev < 3000 ? '一般' : '较差')) . "\n";
}

echo "\n=== 测试完成 ===\n";

// 生成简单的性能报告
$performanceGrade = 'A';
if ($successRate < 90) $performanceGrade = 'B';
if ($successRate < 70) $performanceGrade = 'C';
if ($successRate < 50) $performanceGrade = 'D';
if ($avgResponseTime > 15000) $performanceGrade = chr(ord($performanceGrade) + 1);

echo "综合评级: $performanceGrade\n";

if ($successRate == 100 && $avgResponseTime < 10000) {
    echo "🎉 API性能优秀！\n";
} elseif ($successRate >= 90) {
    echo "👍 API性能良好\n";
} elseif ($successRate >= 70) {
    echo "⚠️ API性能一般，建议优化\n";
} else {
    echo "❌ API性能较差，需要检查\n";
}
?>
