<?php
// 清理配置历史记录中的零变更记录
echo "=== 配置历史记录清理工具 ===\n\n";

$historyDir = 'config_history';
$indexFile = $historyDir . '/index.json';

// 1. 检查当前状态
echo "1. 检查当前状态...\n";
if (!file_exists($indexFile)) {
    echo "   ❌ 索引文件不存在\n";
    exit(1);
}

$index = json_decode(file_get_contents($indexFile), true);
if (!$index) {
    echo "   ❌ 索引文件格式错误\n";
    exit(1);
}

$totalRecords = count($index);
$zeroChangeRecords = array_filter($index, function($record) {
    return $record['change_count'] == 0;
});
$zeroChangeCount = count($zeroChangeRecords);

echo "   总记录数: $totalRecords\n";
echo "   零变更记录数: $zeroChangeCount\n";

if ($zeroChangeCount == 0) {
    echo "   ✅ 没有需要清理的记录\n";
    exit(0);
}

// 2. 显示将要删除的记录
echo "\n2. 将要删除的零变更记录:\n";
foreach ($zeroChangeRecords as $record) {
    echo "   - {$record['file']} ({$record['timestamp']})\n";
}

// 3. 确认删除
echo "\n是否继续删除这些记录？(y/N): ";
$handle = fopen("php://stdin", "r");
$line = fgets($handle);
fclose($handle);

if (trim(strtolower($line)) !== 'y') {
    echo "操作已取消\n";
    exit(0);
}

// 4. 执行清理
echo "\n3. 执行清理...\n";
$deletedCount = 0;
$failedCount = 0;

foreach ($zeroChangeRecords as $record) {
    $filePath = $historyDir . '/' . $record['file'];
    if (file_exists($filePath)) {
        if (unlink($filePath)) {
            echo "   ✅ 已删除: {$record['file']}\n";
            $deletedCount++;
        } else {
            echo "   ❌ 删除失败: {$record['file']}\n";
            $failedCount++;
        }
    } else {
        echo "   ⚠️  文件不存在: {$record['file']}\n";
    }
}

// 5. 更新索引文件
echo "\n4. 更新索引文件...\n";
$newIndex = array_filter($index, function($record) {
    return $record['change_count'] > 0;
});

// 重新排序数组索引
$newIndex = array_values($newIndex);

if (file_put_contents($indexFile, json_encode($newIndex, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT))) {
    echo "   ✅ 索引文件已更新\n";
} else {
    echo "   ❌ 索引文件更新失败\n";
}

// 6. 显示清理结果
echo "\n=== 清理完成 ===\n";
echo "删除文件数: $deletedCount\n";
echo "失败数: $failedCount\n";
echo "剩余记录数: " . count($newIndex) . "\n";

// 7. 验证清理结果
echo "\n5. 验证清理结果...\n";
$remainingFiles = glob($historyDir . '/config_*.json');
$indexRecords = count($newIndex);
$actualFiles = count($remainingFiles);

if ($indexRecords == $actualFiles) {
    echo "   ✅ 索引与实际文件数量一致\n";
} else {
    echo "   ⚠️  索引记录数($indexRecords) 与实际文件数($actualFiles) 不一致\n";
}

// 检查是否还有零变更记录
$remainingZeroChanges = 0;
foreach ($newIndex as $record) {
    if ($record['change_count'] == 0) {
        $remainingZeroChanges++;
    }
}

if ($remainingZeroChanges == 0) {
    echo "   ✅ 所有零变更记录已清理\n";
} else {
    echo "   ⚠️  仍有 $remainingZeroChanges 条零变更记录\n";
}

echo "\n清理工具执行完成！\n";
