[2025-06-06 11:34:30] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目内容，包括题目类型、题干、选项等信息。不需要解答题目，只需要完整准确地识别所有文字内容。"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"请按照以下格式输出：\\r\\n1. 题目类型：（单选题\\\/多选题\\\/判断题）\\r\\n2. 题目内容：（完整的题干）\\r\\n3. 选项内容：（所有选项的完整内容）\\r\\n4. 题干图片：（如果题目下方有图片，请描述图片内容）\\r\\n\\r\\n要求：\\r\\n- 完整准确地识别所有文字内容\\r\\n- 不要解答题目，只需要识别题目内容\\r\\n- 保持原有的格式和选项标识（A、B、C、D或Y、N）"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"detail":"high"}}
[2025-06-06 11:34:49] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目内容，包括题目类型、题干、选项等信息。不需要解答题目，只需要完整准确地识别所有文字内容。"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"请按照以下格式输出：\\r\\n1. 题目类型：（单选题\\\/多选题\\\/判断题）\\r\\n2. 题目内容：（完整的题干）\\r\\n3. 选项内容：（所有选项的完整内容）\\r\\n4. 题干图片：（如果题目下方有图片，请描述图片内容）\\r\\n\\r\\n要求：\\r\\n- 完整准确地识别所有文字内容\\r\\n- 不要解答题目，只需要识别题目内容\\r\\n- 保持原有的格式和选项标识（A、B、C、D或Y、N）"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"detail":"high"}}
[2025-06-06 11:35:12] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目内容，包括题目类型、题干、选项等信息。不需要解答题目，只需要完整准确地识别所有文字内容。"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"请按照以下格式输出：\\r\\n1. 题目类型：（单选题\\\/多选题\\\/判断题）\\r\\n2. 题目内容：（完整的题干）\\r\\n3. 选项内容：（所有选项的完整内容）\\r\\n4. 题干图片：（如果题目下方有图片，请描述图片内容）\\r\\n\\r\\n要求：\\r\\n- 完整准确地识别所有文字内容\\r\\n- 不要解答题目，只需要识别题目内容\\r\\n- 保持原有的格式和选项标识（A、B、C、D或Y、N）"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"detail":"high"}}
[2025-06-06 11:49:07] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目内容，包括题目类型、题干、选项信息。不需要解答题目，只需要完整准确地识别所有文字内容。返回json格式"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 11:49:50] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目内容，包括题目类型、题干、选项信息。不需要解答题目，只需要完整准确地识别所有文字内容。返回json格式"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 11:55:28] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目内容，包括题目类型、题干、选项信息。不需要解答题目，只需要完整准确地识别所有文字内容。返回json格式示例{\r\n  \"question_type\": \"单选题\/多选题\/判断题\",\r\n  \"question_text\": \"题干内容\",\r\n  \"options\": {\r\n    \"A\": \"选项内容\",\r\n    \"B\": \"选项内容\",\r\n    \"C\": \"判断题不存在此选项\",\r\n    \"D\": \"判断题不存在此选项\",\r\n  }\r\n}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 11:56:51] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目内容，包括题目类型、题干、选项信息。不需要解答题目，只需要完整准确地识别所有文字内容。返回json格式示例{\r\n  \"question_type\": \"单选题\/多选题\/判断题\",\r\n  \"question_text\": \"题干内容不包含题目序号\",\r\n  \"options\": {\r\n    \"A\": \"选项内容\",\r\n    \"B\": \"选项内容\",\r\n    \"C\": \"选项内容\",\r\n    \"D\": \"选项内容\",\r\n  }\r\n}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 11:58:20] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目内容，包括题目类型、题干、选项信息。不需要解答题目，只需要完整准确地识别所有文字内容。返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 11:59:55] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、不含序号的题目、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:02:16] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、不含序号的题目、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/02.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:02:42] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、不含序号的题目、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/02.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:05:11] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/02.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:07:11] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/02.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:13:24] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/02.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:13:54] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/02.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:14:36] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/02.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:15:18] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息（输出不要换行）。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/02.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:15:45] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息（输出不要换行）。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/02.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:16:34] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息（忽略换行）。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/02.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:17:04] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/02.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:17:35] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/08.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:18:03] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/08.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:18:34] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/08.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:23:08] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/08.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:25:09] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:25:55] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:30:46] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:31:03] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:31:18] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:32:34] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",\"Y\": \"正确\",\"N\": \"错误\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:32:44] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",\"Y\": \"正确\",\"N\": \"错误\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:32:48] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",\"Y\": \"正确\",\"N\": \"错误\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:32:51] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",\"Y\": \"正确\",\"N\": \"错误\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:32:54] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",\"Y\": \"正确\",\"N\": \"错误\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:32:56] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",\"Y\": \"正确\",\"N\": \"错误\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:32:58] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",\"Y\": \"正确\",\"N\": \"错误\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:33:00] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",\"Y\": \"正确\",\"N\": \"错误\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:33:03] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",\"Y\": \"正确\",\"N\": \"错误\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:33:05] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",\"Y\": \"正确\",\"N\": \"错误\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:33:07] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",\"Y\": \"正确\",\"N\": \"错误\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:33:09] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",\"Y\": \"正确\",\"N\": \"错误\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:33:11] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",\"Y\": \"正确\",\"N\": \"错误\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:33:14] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",\"Y\": \"正确\",\"N\": \"错误\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:33:16] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",\"Y\": \"正确\",\"N\": \"错误\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:33:18] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",\"Y\": \"正确\",\"N\": \"错误\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:33:21] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",\"Y\": \"正确\",\"N\": \"错误\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:33:25] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",\"Y\": \"正确\",\"N\": \"错误\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:33:29] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",\"Y\": \"正确\",\"N\": \"错误\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:33:32] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",\"Y\": \"正确\",\"N\": \"错误\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:34:07] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（但不包含问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",\"Y\": \"正确\",\"N\": \"错误\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/08.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:35:07] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（不包含问题序号与括号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"Y\": \"正确\",\"N\": \"错误\",\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/08.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:35:12] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（不包含问题序号与括号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"Y\": \"正确\",\"N\": \"错误\",\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/08.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:36:07] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（不识别问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/08.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:36:11] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（不识别问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/08.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:36:17] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（不识别问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/08.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:36:24] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（不识别问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/08.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:36:57] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（不识别问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:37:05] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（不识别问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:37:08] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（不识别问题序号）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:37:28] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（需要准确识别）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:37:32] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目，包括题目类型、问题内容（需要准确识别）、选项信息。不需要解答题目，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:38:47] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目的类型、题干、选项，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:38:51] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目的类型、题干、选项，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:39:05] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目的类型、题干、选项，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":"如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号"}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:39:26] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"请精准识别图片中的考试题目的类型、题干、选项，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:39:31] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"请精准识别图片中的考试题目的类型、题干、选项，返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:39:50] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:39:55] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:40:08] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"不含序号的题目内容\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:40:50] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:41:08] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/02.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:41:21] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/03.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:41:27] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/04.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:41:38] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/05.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:42:10] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格格式化返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/05.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:42:17] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格格式化返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/05.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:42:36] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:42:46] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/02.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:42:53] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/04.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high"}}
[2025-06-06 12:53:25] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":0}}
[2025-06-06 12:54:13] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":0}}
[2025-06-06 12:55:19] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/04.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":0}}
[2025-06-06 12:55:38] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":0}}
[2025-06-06 12:55:49] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/02.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":0}}
[2025-06-06 12:56:01] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/03.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":0}}
[2025-06-06 12:56:11] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/04.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":0}}
[2025-06-06 12:56:18] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/05.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":0}}
[2025-06-06 12:56:31] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/06.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":0}}
[2025-06-06 12:56:42] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/07.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":0}}
[2025-06-06 12:57:01] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/08.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":0}}
[2025-06-06 12:57:09] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":0}}
[2025-06-06 12:57:19] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/10.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":0}}
[2025-06-06 12:57:28] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":0}}
[2025-06-06 12:57:41] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/18.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":0}}
[2025-06-06 12:58:23] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/18.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":-2}}
[2025-06-06 12:58:26] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/18.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":-2}}
[2025-06-06 12:58:34] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/05.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":-2}}
[2025-06-06 12:58:40] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/08.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":-2}}
[2025-06-06 12:58:49] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/10.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":-2}}
[2025-06-06 12:58:56] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/11.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":-2}}
[2025-06-06 12:59:23] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/10.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":-2}}
[2025-06-06 12:59:39] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/14.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":-2}}
[2025-06-06 12:59:47] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/15.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":-2}}
[2025-06-06 12:59:55] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/16.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":-2}}
[2025-06-06 13:00:03] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/17.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":-2}}
[2025-06-06 13:00:16] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/18.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":-2}}
[2025-06-06 13:00:26] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/69.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":0,"presence_penalty":-2}}
[2025-06-06 13:01:05] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/69.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"low","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:01:08] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/69.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"low","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:01:14] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"low","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:01:22] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/02.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"low","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:01:28] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/03.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"low","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:01:32] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/04.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"low","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:01:35] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/05.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"low","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:01:42] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/06.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"low","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:01:45] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/07.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"low","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:01:54] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/08.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"low","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:02:06] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"low","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:02:21] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"low","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:02:44] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"low","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:03:19] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.7,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"low","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:03:23] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.7,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"low","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:03:38] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"low","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:03:43] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"low","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:04:09] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:04:20] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:04:25] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:04:33] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:04:39] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:04:58] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:05:01] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:05:06] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:05:22] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.75,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:05:25] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.75,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:05:37] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.75,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:05:40] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.75,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:05:53] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0.2,"top_p":0.75,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:05:56] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0.2,"top_p":0.75,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:06:10] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:06:16] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:06:49] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0.5,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:06:59] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0.5,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:07:01] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0.5,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:07:03] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0.5,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:07:05] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0.5,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:07:07] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0.5,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:07:28] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0.5,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:07:39] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0.5,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:07:42] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0.5,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:07:47] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0.5,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:07:50] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0.5,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:07:53] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0.5,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:08:22] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:08:27] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:08:30] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:08:50] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:08:53] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:08:55] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:08:58] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:09:12] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":0.07,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:09:23] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:09:26] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:09:29] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:09:32] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:09:35] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:09:38] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":1,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:09:50] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:09:55] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:09:59] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:10:07] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":1,"top_k":100,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:11:21] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":1,"top_k":50,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:11:25] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":1,"top_k":50,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:11:29] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":1,"top_p":1,"top_k":50,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:11:55] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":99,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:11:58] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":99,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:12:01] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":99,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:12:04] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":99,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:12:41] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"问题题干，必须完整，禁止丢弃题内文字。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":99,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:12:46] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"问题题干，必须完整，禁止丢弃题内文字。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":99,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:12:53] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"问题题干，必须完整，禁止丢弃题内文字。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":99,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:13:08] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"问题题干，必须完整，禁止丢弃题内文字。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:13:12] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"问题题干，必须完整，禁止丢弃题内文字。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:13:22] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"问题题干，必须完整，禁止丢弃题内文字。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:13:31] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"问题题干，必须完整，禁止丢弃题内文字。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/02.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:13:38] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"问题题干，必须完整，禁止丢弃题内文字。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/03.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:13:57] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"问题题干，必须完整，禁止丢弃题内文字以及标题序号。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/03.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:14:00] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"问题题干，必须完整，禁止丢弃题内文字以及标题序号。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:14:04] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"问题题干，必须完整，禁止丢弃题内文字以及标题序号。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/02.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:14:10] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"问题题干，必须完整，禁止丢弃题内文字以及标题序号。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"精准的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/02.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:15:14] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"必须完整识别，禁止缺少数字。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/02.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:15:17] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"必须完整识别，禁止缺少数字。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/02.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:15:26] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"必须完整识别，禁止缺少数字。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:15:33] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"必须完整识别，禁止缺少数字。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/03.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:15:37] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"必须完整识别，禁止缺少数字。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/04.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:15:55] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"必须完整识别，禁止缺少数字。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/07.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:16:25] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"必须完整识别包括序号。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/07.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:16:34] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"必须完整识别包括序号。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:16:38] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"必须完整识别包括序号。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/02.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:16:41] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"必须完整识别包括序号。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/03.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:16:47] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"必须完整识别包括序号。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/04.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:17:19] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"完整精准识别序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/04.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:17:25] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"完整精准识别序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:17:29] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"完整精准识别序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/02.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:17:32] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"完整精准识别序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/03.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:17:39] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"完整精准识别序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/04.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:17:43] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"完整精准识别序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/05.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:17:56] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"完整精准识别序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:18:03] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"完整精准识别序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/25.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:18:22] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"完整精准识别序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/25.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:18:29] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"完整精准识别序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/26.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:19:00] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"完整精准识别序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/26.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-1}}
[2025-06-06 13:19:55] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准识别问题序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/26.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:19:58] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准识别问题序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/26.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:20:04] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准识别问题序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:20:09] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准识别问题序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/25.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:20:13] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准识别问题序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:20:20] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准识别问题序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:20:23] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准识别问题序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:20:33] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准识别问题序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题\/多选题\/判断题\",\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/29.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:21:10] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准识别问题序号与问题内容。严格标准返回json格式示例{\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/29.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:21:24] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准识别问题序号与问题内容。严格标准返回json格式示例{\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:21:34] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准识别问题序号与问题内容。严格标准返回json格式示例{\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:23:01] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准识别问题序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:23:04] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准识别问题序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:23:13] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准识别问题序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/21.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:23:17] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准识别问题序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:23:23] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准识别问题序号与问题内容。严格标准返回json格式示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/24.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:25:01] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准识别问题序号与问题内容。严格标准返回json格式示例{\\\"question_type\\\": \\\"单选题or多选题or判断题\\\"\\\"question_text\\\": \\\"完整的问题\\\",\\\"options\\\": {\\\"A\\\": \\\"选项内容\\\",\\\"B\\\": \\\"选项内容\\\",\\\"C\\\": \\\"选项内容\\\",\\\"D\\\": \\\"选项内容\\\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/24.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:26:40] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准识别问提号与内容。严格标准返回json格式示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/24.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:26:54] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准识别问提号与内容。严格标准返回json格式示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/24.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:29:55] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准识别问提号与内容。严格标准返回json格式示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:30:07] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准识别问提号与内容。严格标准返回json格式示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:43:55] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。需要丢弃识别到的问题序号。严格标准返回json格式示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:43:57] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。需要丢弃识别到的问题序号。严格标准返回json格式示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:44:00] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。需要丢弃识别到的问题序号。严格标准返回json格式示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:44:33] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"需要丢弃识别到的问题序号。精准且完整的识别题目内容。严格标准返回json格式示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:45:38] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，响应前需要对question_text作序号移除"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:45:41] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，响应前需要对question_text作序号移除"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:45:44] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，响应前需要对question_text作序号移除"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:46:06] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"丢弃识别到的问题序号。精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/09.jpg"},{"text":""}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:46:31] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"丢弃识别到的问题序号。精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":"丢弃识别到的问题序号。"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:46:36] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"丢弃识别到的问题序号。精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":"丢弃识别到的问题序号。"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:47:22] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":"question_text内的值不应该出现题目类型以及问题序号"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:47:35] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/01.jpg"},{"text":"question_text内的值不应该出现题目类型以及问题序号"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:47:41] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/02.jpg"},{"text":"question_text内的值不应该出现题目类型以及问题序号"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:47:47] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/03.jpg"},{"text":"question_text内的值不应该出现题目类型以及问题序号"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:47:52] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/04.jpg"},{"text":"question_text内的值不应该出现题目类型以及问题序号"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:47:59] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/05.jpg"},{"text":"question_text内的值不应该出现题目类型以及问题序号"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:48:15] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/06.jpg"},{"text":"question_text内的值不应该出现题目类型以及问题序号"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:48:24] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/07.jpg"},{"text":"question_text内的值不应该出现题目类型以及问题序号"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:48:34] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":"question_text内的值不应该出现题目类型以及问题序号"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:50:17] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/11.jpg"},{"text":"question_text内的值不应该出现题目类型以及问题序号"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:50:59] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/20.jpg"},{"text":"question_text内的值不应该出现题目类型以及问题序号"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:51:54] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/41.jpg"},{"text":"question_text内的值不应该出现题目类型以及问题序号"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:52:40] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/55.jpg"},{"text":"question_text内的值不应该出现题目类型以及问题序号"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:52:58] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/57.jpg"},{"text":"question_text内的值不应该出现题目类型以及问题序号"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:53:50] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/57.jpg"},{"text":"question_text内的值不应该出现题目类型以及问题序号，如果没有识别到question_type样本，则返回图片不完整。"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:53:55] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/57.jpg"},{"text":"question_text内的值不应该出现题目类型以及问题序号，如果没有识别到question_type样本，则返回图片不完整。"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:54:32] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/57.jpg"},{"text":"question_text内的值不应该出现题目类型以及问题序号，如果认为题目不完整则返回图片不完整"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:54:51] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/57.jpg"},{"text":"question_text内的值不应该出现题目类型以及问题序号。"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:57:05] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。判断问题题目完整性，不完整返回示例{\"ocr_bug\": \"图片不完整\"}，严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/57.jpg"},{"text":"question_text内的值不应该出现题目类型以及问题序号。"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:57:12] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容。判断问题题目完整性，不完整返回示例{\"ocr_bug\": \"图片不完整\"}，严格标准返回json格式，示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/57.jpg"},{"text":"question_text内的值不应该出现题目类型以及问题序号。"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 13:57:36] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容，严格标准返回json格式。示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/57.jpg"},{"text":"question_text内的值不应该出现题目类型以及问题序号。"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
[2025-06-06 14:06:08] qwen_request: {"model":"qwen-vl-plus","input":{"messages":[{"role":"system","content":"精准且完整的识别题目内容，严格标准返回json格式。示例{\"question_type\": \"单选题or多选题or判断题\"\"question_text\": \"完整的问题\",\"options\": {\"A\": \"选项内容\",\"B\": \"选项内容\",\"C\": \"选项内容\",\"D\": \"选项内容\",}}，"},{"role":"user","content":[{"image":"http:\/\/solve.igmdns.com\/img\/23.jpg"},{"text":"question_text内的值不应该出现题目类型以及问题序号。"}]}]},"parameters":{"temperature":0,"top_p":0.01,"top_k":1,"do_sample":false,"response_format":{"type":"json_object"},"detail":"high","frequency_penalty":-2,"presence_penalty":-2}}
