<?php
// 测试 presence_penalty 和 frequency_penalty 参数效果
echo "=== 测试惩罚参数效果对比 ===\n\n";

$testImageUrl = "http://solve.igmdns.com/img/01.jpg";

// 测试不同的惩罚参数组合
$testCases = [
    [
        'name' => '默认设置',
        'frequency_penalty' => 0.0,
        'presence_penalty' => 0.0,
        'description' => '标准行为，无惩罚'
    ],
    [
        'name' => '专注模式',
        'frequency_penalty' => 0.0,
        'presence_penalty' => -0.5,
        'description' => '专注当前话题，深入分析'
    ],
    [
        'name' => '多样化模式',
        'frequency_penalty' => 0.5,
        'presence_penalty' => 0.5,
        'description' => '减少重复，鼓励新角度'
    ]
];

echo "将测试以下参数组合:\n";
foreach ($testCases as $i => $case) {
    echo ($i + 1) . ". {$case['name']}: frequency_penalty={$case['frequency_penalty']}, presence_penalty={$case['presence_penalty']}\n";
    echo "   描述: {$case['description']}\n";
}
echo "\n";

$results = [];

foreach ($testCases as $i => $case) {
    echo "=== 测试 " . ($i + 1) . ": {$case['name']} ===\n";
    
    // 临时修改配置文件
    $configFile = 'config.php';
    $originalConfig = file_get_contents($configFile);
    
    // 读取当前配置
    $config = require $configFile;
    
    // 修改惩罚参数
    $modifiedContent = $originalConfig;
    $modifiedContent = preg_replace(
        "/'frequency_penalty' => [0-9.-]+,/", 
        "'frequency_penalty' => {$case['frequency_penalty']},", 
        $modifiedContent
    );
    $modifiedContent = preg_replace(
        "/'presence_penalty' => [0-9.-]+,/", 
        "'presence_penalty' => {$case['presence_penalty']},", 
        $modifiedContent
    );
    
    // 写入修改后的配置
    file_put_contents($configFile, $modifiedContent);
    
    echo "已设置: frequency_penalty={$case['frequency_penalty']}, presence_penalty={$case['presence_penalty']}\n";
    
    // 发送API请求
    $startTime = microtime(true);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost:8080/index.php');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['image_url' => $testImageUrl]));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 120);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    $endTime = microtime(true);
    $responseTime = ($endTime - $startTime) * 1000;
    
    if ($httpCode === 200 && $response) {
        $responseData = json_decode($response, true);
        
        if ($responseData && isset($responseData['success']) && $responseData['success']) {
            echo "✅ 测试成功 (响应时间: " . number_format($responseTime, 0) . "ms)\n";
            
            // 提取DeepSeek响应内容
            $deepseekContent = '';
            if (isset($responseData['deepseek_response']['content'])) {
                $deepseekContent = $responseData['deepseek_response']['content'];
            }
            
            $results[] = [
                'case' => $case,
                'success' => true,
                'response_time' => $responseTime,
                'content' => $deepseekContent,
                'content_length' => strlen($deepseekContent)
            ];
            
            echo "DeepSeek 响应长度: " . strlen($deepseekContent) . " 字符\n";
            echo "DeepSeek 响应预览: " . substr($deepseekContent, 0, 100) . "...\n";
            
        } else {
            echo "❌ API调用失败: " . ($responseData['error'] ?? '未知错误') . "\n";
            $results[] = [
                'case' => $case,
                'success' => false,
                'error' => $responseData['error'] ?? '未知错误'
            ];
        }
    } else {
        echo "❌ HTTP错误: $httpCode\n";
        $results[] = [
            'case' => $case,
            'success' => false,
            'error' => "HTTP错误: $httpCode"
        ];
    }
    
    echo "\n";
    
    // 等待一下再进行下一个测试
    if ($i < count($testCases) - 1) {
        echo "等待 3 秒...\n\n";
        sleep(3);
    }
}

// 恢复原始配置
file_put_contents($configFile, $originalConfig);
echo "✅ 已恢复原始配置\n\n";

// 分析结果
echo "=== 📊 结果分析 ===\n";

$successfulResults = array_filter($results, function($r) { return $r['success']; });

if (count($successfulResults) > 1) {
    echo "成功测试数量: " . count($successfulResults) . "\n\n";
    
    foreach ($successfulResults as $result) {
        $case = $result['case'];
        echo "🔍 {$case['name']}:\n";
        echo "   • 响应时间: " . number_format($result['response_time'], 0) . "ms\n";
        echo "   • 内容长度: {$result['content_length']} 字符\n";
        echo "   • frequency_penalty: {$case['frequency_penalty']}\n";
        echo "   • presence_penalty: {$case['presence_penalty']}\n";
        
        // 分析内容特征
        $content = $result['content'];
        $sentences = explode('。', $content);
        $sentenceCount = count(array_filter($sentences, function($s) { return trim($s) !== ''; }));
        
        echo "   • 句子数量: $sentenceCount\n";
        
        // 计算词汇多样性（简单指标）
        $words = preg_split('/[\s，。；：！？]+/', $content);
        $uniqueWords = array_unique(array_filter($words, function($w) { return strlen(trim($w)) > 1; }));
        $diversity = count($uniqueWords) / max(count($words), 1);
        
        echo "   • 词汇多样性: " . number_format($diversity * 100, 1) . "%\n";
        echo "\n";
    }
    
    // 对比分析
    echo "📈 对比分析:\n";
    
    $responseTimes = array_column($successfulResults, 'response_time');
    $contentLengths = array_column($successfulResults, 'content_length');
    
    echo "• 响应时间范围: " . number_format(min($responseTimes), 0) . "ms - " . number_format(max($responseTimes), 0) . "ms\n";
    echo "• 内容长度范围: " . min($contentLengths) . " - " . max($contentLengths) . " 字符\n";
    
    if (count($successfulResults) >= 2) {
        $defaultResult = $successfulResults[0];
        $lastResult = end($successfulResults);
        
        $timeDiff = $lastResult['response_time'] - $defaultResult['response_time'];
        $lengthDiff = $lastResult['content_length'] - $defaultResult['content_length'];
        
        echo "• 时间差异: " . ($timeDiff > 0 ? '+' : '') . number_format($timeDiff, 0) . "ms\n";
        echo "• 长度差异: " . ($lengthDiff > 0 ? '+' : '') . $lengthDiff . " 字符\n";
    }
    
} else {
    echo "⚠️ 成功测试数量不足，无法进行对比分析\n";
}

echo "\n=== 📋 参数使用建议 ===\n";
echo "基于测试结果，以下是不同场景的推荐设置:\n\n";

echo "🎯 驾照考试题目解析 (当前场景):\n";
echo "   • frequency_penalty: 0.0\n";
echo "   • presence_penalty: 0.0\n";
echo "   • 理由: 保持标准化答案，避免过度创新\n\n";

echo "📝 创意内容生成:\n";
echo "   • frequency_penalty: 0.5-1.0\n";
echo "   • presence_penalty: 0.5-1.0\n";
echo "   • 理由: 鼓励多样性和新颖性\n\n";

echo "🔬 专业技术分析:\n";
echo "   • frequency_penalty: 0.0-0.3\n";
echo "   • presence_penalty: -0.5-0.0\n";
echo "   • 理由: 专注深度分析，允许适度重复\n\n";

echo "=== 测试完成 ===\n";
echo "✅ presence_penalty 参数功能正常\n";
echo "✅ 可以通过配置管理器调整参数\n";
echo "🎯 建议根据具体使用场景选择合适的参数值\n";
?>
