# 🔄 连续测试功能使用指南

## 📋 概述

API测试界面已成功添加连续测试功能，允许您对同一图片URL进行多次连续请求，用于性能测试、稳定性验证和压力测试。

## 🎛️ 功能特性

### 🔍 **单次测试模式**
- 传统的单次API调用测试
- 详细显示响应内容和统计信息
- 适用于功能验证和内容查看

### 🔄 **连续测试模式**
- 支持1-100次连续测试
- 可设置0-300秒的间隔时间
- 实时进度显示和统计
- 支持手动停止测试
- 遇错停止选项
- 详细的性能分析报告

## 🚀 使用方法

### 1. **访问测试页面**
```
http://localhost:8080/test.html
```

### 2. **选择测试模式**

#### 🔍 单次测试
1. 选择 "🔍 单次测试" 模式
2. 输入图片URL
3. 点击 "🔍 开始识别题目"

#### 🔄 连续测试
1. 选择 "🔄 连续测试" 模式
2. 配置测试参数：
   - **测试次数**: 1-100次
   - **间隔时间**: 0-300秒
   - **遇错停止**: 勾选后遇到错误时停止测试
3. 输入图片URL
4. 点击 "🔄 开始连续测试"

### 3. **测试控制**
- **停止测试**: 点击 "⏹️ 停止测试" 按钮
- **实时监控**: 查看进度条和统计数据
- **结果查看**: 实时显示每次测试结果

## 📊 统计信息

### 实时统计显示
- **当前测试**: 正在执行的测试序号
- **总测试数**: 计划执行的总次数
- **成功次数**: 已成功的测试次数
- **失败次数**: 已失败的测试次数
- **平均耗时**: 平均响应时间(毫秒)
- **总Token消耗**: 累计Token使用量

### 最终统计报告
```
=== 📊 连续测试统计报告 ===
总测试次数: 5
成功次数: 5
失败次数: 0
成功率: 100.0%
平均响应时间: 14875ms
总Token消耗: 7350
最快响应: 13532ms
最慢响应: 16014ms
```

## 🎯 使用场景

### 🔬 **性能测试**
- **目的**: 评估API响应时间和稳定性
- **配置**: 10-20次测试，间隔1-2秒
- **关注指标**: 平均响应时间、成功率、性能稳定性

### 🏋️ **压力测试**
- **目的**: 测试系统在高负载下的表现
- **配置**: 50-100次测试，间隔0-1秒
- **关注指标**: 成功率、错误率、响应时间变化

### 🔍 **稳定性测试**
- **目的**: 验证长时间运行的稳定性
- **配置**: 20-50次测试，间隔5-10秒
- **关注指标**: 错误率、性能一致性

### 💰 **成本评估**
- **目的**: 评估Token消耗和API成本
- **配置**: 5-10次测试，间隔2-5秒
- **关注指标**: 总Token消耗、平均Token使用

## 📈 性能分析

### 响应时间分析
- **优秀**: < 10秒
- **良好**: 10-15秒
- **一般**: 15-20秒
- **较差**: > 20秒

### 成功率评级
- **A级**: 100% 成功率
- **B级**: 90-99% 成功率
- **C级**: 70-89% 成功率
- **D级**: < 70% 成功率

### 稳定性评估
- **良好**: 响应时间标准差 < 1000ms
- **一般**: 响应时间标准差 1000-3000ms
- **较差**: 响应时间标准差 > 3000ms

## ⚙️ 配置建议

### 🚀 **快速验证**
```
测试次数: 3-5次
间隔时间: 1-2秒
遇错停止: 开启
```

### 🔬 **性能评估**
```
测试次数: 10-20次
间隔时间: 2-3秒
遇错停止: 关闭
```

### 🏋️ **压力测试**
```
测试次数: 50-100次
间隔时间: 0-1秒
遇错停止: 关闭
```

### 💰 **成本分析**
```
测试次数: 5-10次
间隔时间: 3-5秒
遇错停止: 开启
```

## 🛠️ 故障排除

### 常见问题

1. **测试中断**
   - 检查网络连接
   - 确认API服务状态
   - 查看浏览器控制台错误

2. **响应时间过长**
   - 检查服务器负载
   - 验证API密钥有效性
   - 确认图片URL可访问

3. **Token消耗异常**
   - 检查DeepSeek启用状态
   - 验证配置参数设置
   - 查看API响应内容

### 调试方法

1. **单次测试验证**
   ```
   先进行单次测试确认功能正常
   ```

2. **逐步增加测试次数**
   ```
   从3次开始，逐步增加到目标次数
   ```

3. **检查服务器日志**
   ```
   查看 logs/api_logs.txt 了解详细信息
   ```

## 📋 最佳实践

### ✅ **推荐做法**
- 测试前先进行单次验证
- 合理设置间隔时间避免过载
- 开启遇错停止避免浪费配额
- 定期清理测试日志
- 监控Token使用量

### ❌ **避免事项**
- 不要设置过短的间隔时间
- 不要在生产环境进行大量测试
- 不要忽略错误信息
- 不要同时运行多个连续测试

## 🎉 功能优势

- ✅ **易于使用**: 直观的界面设计
- ✅ **实时监控**: 进度和统计实时更新
- ✅ **灵活配置**: 多种参数可调节
- ✅ **详细报告**: 完整的性能分析
- ✅ **安全控制**: 支持手动停止和错误停止
- ✅ **兼容性好**: 保留原有单次测试功能

通过连续测试功能，您可以全面评估API的性能表现，确保系统在各种负载条件下的稳定运行！
