# 🔌 DeepSeek 开关功能使用指南

## 📋 概述

系统已成功添加了 DeepSeek API 的开关控制功能，允许您选择性地启用或禁用 DeepSeek API 调用，实现灵活的 API 使用策略。

## 🎛️ 功能特性

### ✅ **启用状态 (enabled: true)**
- 正常调用 DeepSeek API
- 获得双重 AI 分析结果
- 消耗 DeepSeek API 配额
- 提供完整的题目解析

### ❌ **禁用状态 (enabled: false)**
- 跳过 DeepSeek API 调用
- 只使用 Qwen API 进行图片识别
- 节省 DeepSeek API 配额
- 减少响应时间
- 降低成本

## 🔧 配置方法

### 1. **通过配置管理器界面**

访问 `http://localhost:8080/config_manager.php`

在 **DeepSeek-Chat 配置** 区域找到：

```
🔌 启用状态:
[✅ 启用 DeepSeek API] ▼
[❌ 禁用 DeepSeek API]
```

- 选择 "✅ 启用 DeepSeek API" - 正常调用
- 选择 "❌ 禁用 DeepSeek API" - 跳过调用

### 2. **使用预设配置**

系统提供了多种预设配置：

| 预设模式 | DeepSeek 状态 | 适用场景 |
|---------|--------------|----------|
| 🔒 严格模式 | ✅ 启用 | 需要确定性输出和完整分析 |
| 🎨 创意模式 | ✅ 启用 | 需要多样化和创造性输出 |
| ⚖️ 平衡模式 | ✅ 启用 | 通用场景，平衡性能和质量 |
| 🚀 仅Qwen模式 | ❌ 禁用 | 快速识别，节省成本 |
| 🐛 调试模式 | ✅ 启用 | 开发调试，详细日志 |

### 3. **直接编辑配置文件**

编辑 `config.php` 文件：

```php
'deepseek' => [
    'enabled' => false,  // 设置为 false 禁用，true 启用
    'api_url' => 'https://api.deepseek.com/chat/completions',
    // ... 其他配置
]
```

## 📊 响应格式对比

### 启用状态响应
```json
{
    "success": true,
    "qwen_response": {
        "content": "题目识别结果...",
        "duration_ms": 2192.74,
        "tokens": {"total_tokens": 1097, "input_tokens": 1002, "output_tokens": 95}
    },
    "deepseek_response": {
        "content": "详细题目分析和答案解析...",
        "duration_ms": 8234.56,
        "tokens": {"total_tokens": 372, "input_tokens": 239, "output_tokens": 133}
    }
}
```

### 禁用状态响应
```json
{
    "success": true,
    "qwen_response": {
        "content": "题目识别结果...",
        "duration_ms": 2192.74,
        "tokens": {"total_tokens": 1097, "input_tokens": 1002, "output_tokens": 95}
    },
    "deepseek_response": {
        "content": "DeepSeek API 已禁用，未调用",
        "duration_ms": 0,
        "tokens": {"total_tokens": 0, "input_tokens": 0, "output_tokens": 0},
        "status": "disabled"
    }
}
```

## 🎯 使用场景建议

### 🚀 **建议禁用 DeepSeek 的场景**

1. **快速批量处理**
   - 大量图片需要快速识别
   - 只需要基础的题目内容提取

2. **成本控制**
   - API 配额有限
   - 需要降低调用成本

3. **开发测试**
   - 功能测试阶段
   - 不需要完整的分析结果

4. **网络环境限制**
   - DeepSeek API 访问不稳定
   - 需要确保服务可用性

### ✅ **建议启用 DeepSeek 的场景**

1. **完整题目分析**
   - 需要详细的答案解析
   - 要求高质量的内容输出

2. **生产环境**
   - 正式的应用场景
   - 用户需要完整的服务

3. **质量要求高**
   - 对识别准确性要求高
   - 需要双重验证

## 📈 性能对比

| 指标 | 启用 DeepSeek | 禁用 DeepSeek | 差异 |
|------|--------------|--------------|------|
| **响应时间** | ~12-15秒 | ~2-3秒 | 减少 80% |
| **Token 消耗** | Qwen + DeepSeek | 仅 Qwen | 减少 ~25% |
| **API 调用次数** | 2次 | 1次 | 减少 50% |
| **内容完整性** | 完整分析 | 基础识别 | 功能简化 |

## 🔍 监控和日志

### 日志记录
- **启用时**: 记录完整的 Qwen 和 DeepSeek 调用日志
- **禁用时**: 记录 "DeepSeek API 已禁用，跳过调用" 信息

### 日志文件位置
- `logs/api_logs.txt` - 通用日志
- `logs/api_logs_qwen.txt` - Qwen 专用日志
- `logs/api_logs_deepseek.txt` - DeepSeek 专用日志

### 监控指标
- API 调用成功率
- 响应时间统计
- Token 使用量
- 错误率分析

## 🛠️ 故障排除

### 常见问题

1. **配置不生效**
   - 检查配置文件格式
   - 确认布尔值类型正确
   - 重启服务器

2. **界面显示异常**
   - 清除浏览器缓存
   - 检查 JavaScript 控制台错误

3. **日志记录问题**
   - 检查日志目录权限
   - 确认磁盘空间充足

### 调试方法

1. **运行测试脚本**
   ```bash
   php test_deepseek_switch.php
   php test_deepseek_disabled.php
   ```

2. **检查配置状态**
   ```bash
   php -r "print_r(require 'config.php');"
   ```

3. **查看实时日志**
   ```bash
   tail -f logs/api_logs.txt
   ```

## 🎉 总结

DeepSeek 开关功能为您提供了灵活的 API 使用策略：

- ✅ **完全兼容**: 现有功能不受影响
- ✅ **灵活控制**: 可随时启用/禁用
- ✅ **成本优化**: 按需使用，节省配额
- ✅ **性能提升**: 禁用时响应更快
- ✅ **易于管理**: 多种配置方式

通过合理使用这个开关，您可以在功能完整性和性能效率之间找到最佳平衡点！
