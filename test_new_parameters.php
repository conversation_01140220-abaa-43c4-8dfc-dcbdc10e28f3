<?php
// 测试新增的 top_p、top_k、do_sample 参数
echo "=== 测试新增参数功能 ===\n\n";

// 1. 检查配置文件中的新参数
echo "1. 检查配置文件中的新参数...\n";
$config = require 'config.php';

$requiredParams = [
    'qwen' => ['top_p', 'top_k', 'do_sample'],
    'deepseek' => ['top_p', 'top_k', 'do_sample']
];

$allParamsExist = true;

foreach ($requiredParams as $service => $params) {
    echo "   检查 $service 配置:\n";
    foreach ($params as $param) {
        if (isset($config[$service][$param])) {
            $value = $config[$service][$param];
            $type = gettype($value);
            echo "     ✅ $param: $value ($type)\n";
        } else {
            echo "     ❌ $param: 缺失\n";
            $allParamsExist = false;
        }
    }
    echo "\n";
}

if ($allParamsExist) {
    echo "✅ 所有新参数都已正确配置\n\n";
} else {
    echo "❌ 部分参数缺失，请检查配置\n\n";
}

// 2. 验证参数值的合理性
echo "2. 验证参数值的合理性...\n";

// 检查 Qwen 参数
$qwenValid = true;
if ($config['qwen']['top_p'] < 0.01 || $config['qwen']['top_p'] > 1) {
    echo "   ⚠️  Qwen top_p 值超出范围 (0.01-1): " . $config['qwen']['top_p'] . "\n";
    $qwenValid = false;
}

if ($config['qwen']['top_k'] < 1 || $config['qwen']['top_k'] > 100) {
    echo "   ⚠️  Qwen top_k 值超出范围 (1-100): " . $config['qwen']['top_k'] . "\n";
    $qwenValid = false;
}

if (!is_bool($config['qwen']['do_sample'])) {
    echo "   ⚠️  Qwen do_sample 应为布尔值: " . gettype($config['qwen']['do_sample']) . "\n";
    $qwenValid = false;
}

// 检查 DeepSeek 参数
$deepseekValid = true;
if ($config['deepseek']['top_p'] < 0.01 || $config['deepseek']['top_p'] > 1) {
    echo "   ⚠️  DeepSeek top_p 值超出范围 (0.01-1): " . $config['deepseek']['top_p'] . "\n";
    $deepseekValid = false;
}

if ($config['deepseek']['top_k'] < 1 || $config['deepseek']['top_k'] > 100) {
    echo "   ⚠️  DeepSeek top_k 值超出范围 (1-100): " . $config['deepseek']['top_k'] . "\n";
    $deepseekValid = false;
}

if (!is_bool($config['deepseek']['do_sample'])) {
    echo "   ⚠️  DeepSeek do_sample 应为布尔值: " . gettype($config['deepseek']['do_sample']) . "\n";
    $deepseekValid = false;
}

if ($qwenValid && $deepseekValid) {
    echo "   ✅ 所有参数值都在合理范围内\n\n";
} else {
    echo "   ⚠️  部分参数值需要调整\n\n";
}

// 3. 测试配置管理器的参数处理
echo "3. 测试配置管理器的参数处理...\n";

// 模拟 POST 数据
$_POST = [
    'action' => 'update_config',
    'qwen' => [
        'top_p' => '0.9',
        'top_k' => '80',
        'do_sample' => 'true'
    ],
    'deepseek' => [
        'top_p' => '0.8',
        'top_k' => '60',
        'do_sample' => 'false'
    ]
];

// 模拟配置更新逻辑
$newConfig = $config;

// 更新Qwen配置
if (isset($_POST['qwen'])) {
    foreach ($_POST['qwen'] as $key => $value) {
        if (in_array($key, ['temperature', 'top_p'])) {
            $newConfig['qwen'][$key] = (float)$value;
        } elseif (in_array($key, ['timeout', 'max_retries', 'top_k'])) {
            $newConfig['qwen'][$key] = (int)$value;
        } elseif ($key === 'do_sample') {
            $newConfig['qwen'][$key] = $value === 'true';
        } else {
            $newConfig['qwen'][$key] = $value;
        }
    }
}

// 更新DeepSeek配置
if (isset($_POST['deepseek'])) {
    foreach ($_POST['deepseek'] as $key => $value) {
        if (in_array($key, ['temperature', 'top_p', 'frequency_penalty', 'presence_penalty'])) {
            $newConfig['deepseek'][$key] = (float)$value;
        } elseif (in_array($key, ['max_tokens', 'timeout', 'max_retries', 'top_k'])) {
            $newConfig['deepseek'][$key] = (int)$value;
        } elseif ($key === 'do_sample') {
            $newConfig['deepseek'][$key] = $value === 'true';
        } else {
            $newConfig['deepseek'][$key] = $value;
        }
    }
}

// 验证更新结果
echo "   更新前 -> 更新后:\n";
echo "   Qwen top_p: " . $config['qwen']['top_p'] . " -> " . $newConfig['qwen']['top_p'] . "\n";
echo "   Qwen top_k: " . $config['qwen']['top_k'] . " -> " . $newConfig['qwen']['top_k'] . "\n";
echo "   Qwen do_sample: " . ($config['qwen']['do_sample'] ? 'true' : 'false') . " -> " . ($newConfig['qwen']['do_sample'] ? 'true' : 'false') . "\n";
echo "   DeepSeek top_p: " . $config['deepseek']['top_p'] . " -> " . $newConfig['deepseek']['top_p'] . "\n";
echo "   DeepSeek top_k: " . $config['deepseek']['top_k'] . " -> " . $newConfig['deepseek']['top_k'] . "\n";
echo "   DeepSeek do_sample: " . ($config['deepseek']['do_sample'] ? 'true' : 'false') . " -> " . ($newConfig['deepseek']['do_sample'] ? 'true' : 'false') . "\n";

echo "   ✅ 参数处理逻辑正常\n\n";

// 4. 测试 API 请求数据构建
echo "4. 测试 API 请求数据构建...\n";

// 模拟 Qwen 请求数据
$qwenRequestData = [
    'model' => $newConfig['qwen']['model'],
    'input' => [
        'messages' => [
            [
                'role' => $newConfig['qwen']['role'],
                'content' => $newConfig['qwen']['system_content']
            ],
            [
                'role' => 'user',
                'content' => [
                    ['image' => 'https://example.com/test.jpg'],
                    ['text' => $newConfig['qwen']['user_prompt_template']]
                ]
            ]
        ]
    ],
    'parameters' => [
        'temperature' => $newConfig['qwen']['temperature'],
        'top_p' => $newConfig['qwen']['top_p'],
        'top_k' => $newConfig['qwen']['top_k'],
        'do_sample' => $newConfig['qwen']['do_sample'],
        'detail' => $newConfig['qwen']['detail']
    ]
];

// 模拟 DeepSeek 请求数据
$deepseekRequestData = [
    'model' => $newConfig['deepseek']['model'],
    'messages' => [
        [
            'role' => $newConfig['deepseek']['role'],
            'content' => $newConfig['deepseek']['system_content']
        ],
        [
            'role' => 'user',
            'content' => 'test content' . $newConfig['deepseek']['user_prompt_template']
        ]
    ],
    'temperature' => $newConfig['deepseek']['temperature'],
    'max_tokens' => $newConfig['deepseek']['max_tokens'],
    'top_p' => $newConfig['deepseek']['top_p'],
    'top_k' => $newConfig['deepseek']['top_k'],
    'do_sample' => $newConfig['deepseek']['do_sample'],
    'frequency_penalty' => $newConfig['deepseek']['frequency_penalty'],
    'presence_penalty' => $newConfig['deepseek']['presence_penalty']
];

echo "   Qwen 请求参数:\n";
foreach ($qwenRequestData['parameters'] as $key => $value) {
    echo "     $key: " . json_encode($value) . "\n";
}

echo "   DeepSeek 请求参数:\n";
$deepseekParams = ['temperature', 'max_tokens', 'top_p', 'top_k', 'do_sample', 'frequency_penalty', 'presence_penalty'];
foreach ($deepseekParams as $param) {
    if (isset($deepseekRequestData[$param])) {
        echo "     $param: " . json_encode($deepseekRequestData[$param]) . "\n";
    }
}

echo "   ✅ API 请求数据构建正常\n\n";

// 5. 显示参数说明
echo "5. 新参数说明:\n";
echo "   • top_p (0.01-1): 核采样概率阈值，控制候选词汇的累积概率\n";
echo "   • top_k (1-100): 保留概率最高的K个候选词汇\n";
echo "   • do_sample (true/false): 是否启用随机采样\n";
echo "     - true: 启用随机采样，输出更多样化\n";
echo "     - false: 使用贪婪搜索，输出更确定性\n\n";

echo "=== 测试完成 ===\n";
echo "✅ 新参数功能已成功集成到系统中\n";
echo "🎯 建议访问配置管理器页面测试界面控制功能\n";
