# 故障排除指南

## 🔧 常见问题解决方案

### 1. test.php报错 "Unexpected token '<'"

**问题原因**: PHP代码出现语法错误或配置文件加载失败

**解决方案**:
```bash
# 检查PHP语法
php -l index.php
php -l config.php
php -l test.php

# 检查配置文件加载
php -r "var_dump(require 'config.php');"
```

### 2. API调用失败 "Qwen API调用失败"

**问题原因**: 
- API密钥无效
- 图片URL格式不支持
- 网络连接问题
- API服务不可用

**解决方案**:

#### 步骤1: 检查API连接
```bash
php test_api_connection.php
```

#### 步骤2: 启用测试模式
在 `config.php` 中设置:
```php
'enable_test_mode' => true,
```

#### 步骤3: 检查图片URL
Qwen API对图片URL有特殊要求：
- 必须是直接的图片链接
- 支持的格式：JPG, PNG, GIF, BMP
- 图片大小不超过20MB
- URL必须可公开访问

**有效的图片URL示例**:
```
https://example.com/image.jpg
https://domain.com/path/to/image.png
```

**无效的图片URL示例**:
```
https://via.placeholder.com/600x400  (动态生成)
https://domain.com/image?param=value (带参数)
```

### 3. 配置文件问题

**问题**: 配置文件格式错误或缺少参数

**解决方案**:
1. 使用配置管理器: `config_manager.php`
2. 检查必需的配置项:
   - `qwen.api_key`
   - `deepseek.api_key`
   - `qwen.model`
   - `deepseek.model`

### 4. 权限问题

**问题**: 日志文件无法写入

**解决方案**:
```bash
# 创建日志目录
mkdir -p logs

# 设置权限
chmod 755 logs
chmod 666 logs/api_logs.txt
```

### 5. cURL错误

**问题**: 网络连接或SSL证书问题

**解决方案**:
1. 检查网络连接
2. 在代码中已设置 `CURLOPT_SSL_VERIFYPEER => false`
3. 检查防火墙设置

## 🧪 测试模式

### 启用测试模式
在 `config.php` 中设置:
```php
'test' => [
    'enable_test_mode' => true,
    // ...
]
```

### 测试模式功能
- 🔄 跳过真实API调用
- 📝 返回模拟的识别和解答数据
- ⚡ 快速测试系统功能
- 💰 不消耗API配额

### 模拟数据示例

**Qwen模拟响应**:
```
1. 题目类型：单选题
2. 题目内容：驾驶机动车驶近坡道顶端、视距不良时，应怎样做以确保安全？
3. 选项内容：A、减速鸣喇叭示意 B、迅速行驶到坡顶以改善视距 C、长时间开启远光灯提醒对向来车 D、不间断鸣喇叭并加速冲坡
4. 题干图片：无图片
```

**DeepSeek模拟响应**:
```
题目类型：单选题
题目内容：驾驶机动车驶近坡道顶端、视距不良时，应怎样做以确保安全？
选项内容：A：减速鸣喇叭示意；B：迅速行驶到坡顶以改善视距；C：长时间开启远光灯提醒对向来车；D：不间断鸣喇叭并加速冲坡；
正确答案：A
答案解析：在坡道顶端视距不良时，应当减速并鸣喇叭示意，确保行车安全。
```

## 🔍 调试工具

### 1. 系统检查
```bash
php check_setup.php
```

### 2. API连接测试
```bash
php test_api_connection.php
```

### 3. 直接API测试
```bash
php test_qwen_direct.php
```

### 4. 配置调试
```bash
php debug_api.php
```

### 5. 日志查看
访问: `logs/view_logs.php`

## 📋 检查清单

在报告问题前，请确认以下项目：

- [ ] PHP版本 >= 7.4
- [ ] cURL扩展已安装
- [ ] 配置文件存在且格式正确
- [ ] API密钥已正确配置
- [ ] 日志目录可写
- [ ] 网络连接正常
- [ ] 图片URL格式正确
- [ ] API账户余额充足

## 🆘 获取帮助

### 查看详细错误信息
1. 启用调试模式:
   ```php
   'system' => [
       'debug_mode' => true,
       'verbose_logging' => true,
   ]
   ```

2. 查看日志文件: `logs/api_logs.txt`

3. 检查PHP错误日志

### 联系支持
提供以下信息：
- 错误信息的完整截图
- 相关的日志文件内容
- 使用的图片URL
- 系统检查结果

## 🔄 重置系统

如果遇到严重问题，可以重置系统：

1. 备份配置:
   ```bash
   cp config.php config.php.backup
   ```

2. 清空日志:
   ```bash
   > logs/api_logs.txt
   ```

3. 重新配置:
   访问 `config_manager.php` 重新设置参数

4. 运行系统检查:
   ```bash
   php check_setup.php
   ```

## 📈 性能优化

### API调用优化
- 调整超时时间: `timeout`
- 设置重试次数: `max_retries`
- 启用详细日志: `verbose_logging`

### 系统优化
- 定期清理日志文件
- 监控API使用量
- 优化图片大小和格式
