<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>驾照考试题目识别API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="url"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .test-mode {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .test-mode label {
            display: flex;
            align-items: center;
            gap: 5px;
            font-weight: normal;
            cursor: pointer;
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            background: white;
            transition: all 0.3s;
        }
        .test-mode label:hover {
            border-color: #007bff;
        }
        .test-mode input[type="radio"] {
            margin: 0;
        }
        .test-mode input[type="radio"]:checked + span {
            color: #007bff;
            font-weight: bold;
        }
        .test-mode label:has(input:checked) {
            border-color: #007bff;
            background-color: #f0f8ff;
        }
        .continuous-options {
            display: none;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }
        .continuous-options.show {
            display: block;
        }
        .option-row {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 10px;
        }
        .option-row:last-child {
            margin-bottom: 0;
        }
        .option-row label {
            min-width: 100px;
            margin-bottom: 0;
        }
        .option-row input {
            flex: 1;
            max-width: 150px;
        }
        .progress-container {
            display: none;
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
        .progress-container.show {
            display: block;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }
        .progress-fill {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        .progress-text {
            text-align: center;
            font-weight: bold;
            color: #495057;
        }
        .test-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .stat-item {
            background: white;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 2px;
        }
        .stop-btn {
            background-color: #dc3545;
            margin-left: 10px;
        }
        .stop-btn:hover {
            background-color: #c82333;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
            max-height: 500px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .curl-example {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .curl-example h3 {
            margin-top: 0;
            color: #495057;
        }
        .curl-command {
            background-color: #343a40;
            color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            word-break: break-all;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚗 驾照考试题目识别API测试</h1>
        
        <form id="testForm">
            <div class="form-group">
                <label for="imageUrl">图片URL地址:</label>
                <input
                    type="url"
                    id="imageUrl"
                    name="imageUrl"
                    placeholder="https://example.com/exam-image.jpg"
                    required
                >
            </div>

            <div class="form-group">
                <label>测试模式:</label>
                <div class="test-mode">
                    <label>
                        <input type="radio" name="testMode" value="single" checked>
                        <span>🔍 单次测试</span>
                    </label>
                    <label>
                        <input type="radio" name="testMode" value="continuous">
                        <span>🔄 连续测试</span>
                    </label>
                </div>
            </div>

            <div id="continuousOptions" class="continuous-options">
                <div class="option-row">
                    <label for="testCount">测试次数:</label>
                    <input type="number" id="testCount" min="1" max="100" value="5">
                    <span style="color: #6c757d; font-size: 14px;">次 (1-100)</span>
                </div>
                <div class="option-row">
                    <label for="testInterval">间隔时间:</label>
                    <input type="number" id="testInterval" min="0" max="300" value="2">
                    <span style="color: #6c757d; font-size: 14px;">秒 (0-300)</span>
                </div>
                <div class="option-row">
                    <label for="stopOnError">遇错停止:</label>
                    <input type="checkbox" id="stopOnError" checked>
                    <span style="color: #6c757d; font-size: 14px;">勾选后遇到错误时停止测试</span>
                </div>
            </div>

            <div style="display: flex; align-items: center;">
                <button type="submit" id="submitBtn">🔍 开始识别题目</button>
                <button type="button" id="stopBtn" class="stop-btn" style="display: none;">⏹️ 停止测试</button>
            </div>
        </form>

        <div id="progressContainer" class="progress-container">
            <div class="progress-bar">
                <div id="progressFill" class="progress-fill" style="width: 0%;"></div>
            </div>
            <div id="progressText" class="progress-text">准备开始测试...</div>
            <div id="testStats" class="test-stats">
                <div class="stat-item">
                    <div id="currentTest" class="stat-value">0</div>
                    <div class="stat-label">当前测试</div>
                </div>
                <div class="stat-item">
                    <div id="totalTests" class="stat-value">0</div>
                    <div class="stat-label">总测试数</div>
                </div>
                <div class="stat-item">
                    <div id="successCount" class="stat-value">0</div>
                    <div class="stat-label">成功次数</div>
                </div>
                <div class="stat-item">
                    <div id="errorCount" class="stat-value">0</div>
                    <div class="stat-label">失败次数</div>
                </div>
                <div class="stat-item">
                    <div id="avgResponseTime" class="stat-value">0</div>
                    <div class="stat-label">平均耗时(ms)</div>
                </div>
                <div class="stat-item">
                    <div id="totalTokens" class="stat-value">0</div>
                    <div class="stat-label">总Token消耗</div>
                </div>
            </div>
        </div>
        
        <div id="result" class="result" style="display: none;"></div>
        
        <div class="curl-example">
            <h3>📋 cURL命令示例:</h3>
            <p>您也可以使用以下cURL命令来测试API：</p>
            <div class="curl-command" id="curlCommand">
                curl -X POST -H "Content-Type: application/json" -d '{"image_url":"YOUR_IMAGE_URL"}' http://localhost/index.php
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let isTestRunning = false;
        let shouldStopTest = false;
        let testResults = [];

        // 测试模式切换
        document.querySelectorAll('input[name="testMode"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const continuousOptions = document.getElementById('continuousOptions');
                const submitBtn = document.getElementById('submitBtn');

                if (this.value === 'continuous') {
                    continuousOptions.classList.add('show');
                    submitBtn.textContent = '🔄 开始连续测试';
                } else {
                    continuousOptions.classList.remove('show');
                    submitBtn.textContent = '🔍 开始识别题目';
                }
            });
        });

        // 停止测试按钮
        document.getElementById('stopBtn').addEventListener('click', function() {
            shouldStopTest = true;
            this.style.display = 'none';
            document.getElementById('submitBtn').disabled = false;
            document.getElementById('submitBtn').textContent = '🔍 开始识别题目';
        });

        // 表单提交
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const testMode = document.querySelector('input[name="testMode"]:checked').value;
            const imageUrl = document.getElementById('imageUrl').value;
            const curlCommand = document.getElementById('curlCommand');

            // 更新cURL命令示例
            curlCommand.textContent = `curl -X POST -H "Content-Type: application/json" -d '{"image_url":"${imageUrl}"}' ${window.location.origin}/index.php`;

            if (testMode === 'single') {
                await runSingleTest(imageUrl);
            } else {
                await runContinuousTest(imageUrl);
            }
        });

        // 单次测试
        async function runSingleTest(imageUrl) {
            const submitBtn = document.getElementById('submitBtn');
            const resultDiv = document.getElementById('result');

            // 显示加载状态
            submitBtn.disabled = true;
            submitBtn.textContent = '🔄 正在处理...';
            resultDiv.style.display = 'block';
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在调用API，请稍候...\n\n这可能需要几十秒的时间，因为需要：\n1. 调用Qwen-VL-Plus识别图片\n2. 调用DeepSeek-Chat解答题目\n3. 记录日志';

            try {
                const result = await callAPI(imageUrl);
                displaySingleResult(result);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 网络错误\n\n' + error.message;
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = '🔍 开始识别题目';
            }
        }

        // 连续测试
        async function runContinuousTest(imageUrl) {
            const testCount = parseInt(document.getElementById('testCount').value);
            const testInterval = parseInt(document.getElementById('testInterval').value) * 1000; // 转换为毫秒
            const stopOnError = document.getElementById('stopOnError').checked;

            const submitBtn = document.getElementById('submitBtn');
            const stopBtn = document.getElementById('stopBtn');
            const progressContainer = document.getElementById('progressContainer');
            const resultDiv = document.getElementById('result');

            // 初始化状态
            isTestRunning = true;
            shouldStopTest = false;
            testResults = [];

            submitBtn.disabled = true;
            submitBtn.textContent = '🔄 连续测试中...';
            stopBtn.style.display = 'inline-block';
            progressContainer.classList.add('show');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result loading';
            resultDiv.textContent = '连续测试准备中...\n\n';

            // 更新统计显示
            updateStats(0, testCount, 0, 0, 0, 0);

            let successCount = 0;
            let errorCount = 0;
            let totalResponseTime = 0;
            let totalTokens = 0;

            for (let i = 1; i <= testCount; i++) {
                if (shouldStopTest) {
                    resultDiv.textContent += `\n⏹️ 测试已手动停止 (完成 ${i-1}/${testCount} 次)\n`;
                    break;
                }

                // 更新进度
                const progress = (i / testCount) * 100;
                document.getElementById('progressFill').style.width = progress + '%';
                document.getElementById('progressText').textContent = `正在执行第 ${i} 次测试...`;
                updateStats(i, testCount, successCount, errorCount,
                    totalResponseTime > 0 ? Math.round(totalResponseTime / (successCount + errorCount)) : 0,
                    totalTokens);

                try {
                    const startTime = Date.now();
                    const result = await callAPI(imageUrl);
                    const endTime = Date.now();
                    const responseTime = endTime - startTime;

                    testResults.push({
                        testNumber: i,
                        success: result.success,
                        responseTime: responseTime,
                        result: result
                    });

                    if (result.success) {
                        successCount++;
                        totalResponseTime += responseTime;

                        // 计算Token消耗
                        if (result.data.qwen_response && result.data.qwen_response.tokens) {
                            totalTokens += result.data.qwen_response.tokens.total_tokens;
                        }
                        if (result.data.deepseek_response && result.data.deepseek_response.tokens) {
                            totalTokens += result.data.deepseek_response.tokens.total_tokens;
                        }

                        resultDiv.textContent += `✅ 第${i}次测试成功 (${responseTime}ms)\n`;
                    } else {
                        errorCount++;
                        resultDiv.textContent += `❌ 第${i}次测试失败: ${result.error}\n`;

                        if (stopOnError) {
                            resultDiv.textContent += `\n⏹️ 遇到错误，测试停止 (完成 ${i}/${testCount} 次)\n`;
                            break;
                        }
                    }

                } catch (error) {
                    errorCount++;
                    testResults.push({
                        testNumber: i,
                        success: false,
                        error: error.message,
                        responseTime: 0
                    });

                    resultDiv.textContent += `❌ 第${i}次测试网络错误: ${error.message}\n`;

                    if (stopOnError) {
                        resultDiv.textContent += `\n⏹️ 遇到错误，测试停止 (完成 ${i}/${testCount} 次)\n`;
                        break;
                    }
                }

                // 滚动到底部
                resultDiv.scrollTop = resultDiv.scrollHeight;

                // 等待间隔时间（除了最后一次）
                if (i < testCount && !shouldStopTest && testInterval > 0) {
                    resultDiv.textContent += `⏱️ 等待 ${testInterval/1000} 秒...\n`;
                    await sleep(testInterval);
                }
            }

            // 测试完成
            isTestRunning = false;
            submitBtn.disabled = false;
            submitBtn.textContent = '🔍 开始识别题目';
            stopBtn.style.display = 'none';

            // 显示最终统计
            const finalStats = generateFinalStats(testResults, successCount, errorCount, totalResponseTime, totalTokens);
            resultDiv.className = successCount > errorCount ? 'result success' : 'result error';
            resultDiv.textContent += '\n' + finalStats;

            // 更新最终统计显示
            updateStats(testCount, testCount, successCount, errorCount,
                totalResponseTime > 0 ? Math.round(totalResponseTime / (successCount + errorCount)) : 0,
                totalTokens);
            document.getElementById('progressText').textContent = '测试完成';
        }

        // API调用函数
        async function callAPI(imageUrl) {
            const response = await fetch('/index.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    image_url: imageUrl
                })
            });

            const data = await response.json();

            return {
                success: data.success,
                data: data,
                error: data.error
            };
        }

        // 显示单次测试结果
        function displaySingleResult(result) {
            const resultDiv = document.getElementById('result');

            if (result.success) {
                resultDiv.className = 'result success';
                let resultText = '✅ API调用成功！\n\n';

                // 显示Qwen统计信息
                if (result.data.qwen_response) {
                    const qwen = result.data.qwen_response;
                    resultText += '=== 🖼️ Qwen-VL-Plus 统计 ===\n';
                    resultText += `耗时: ${qwen.duration_ms || '未知'}ms\n`;
                    if (qwen.tokens) {
                        resultText += `Token消费: 总计${qwen.tokens.total_tokens} (输入${qwen.tokens.input_tokens} + 输出${qwen.tokens.output_tokens})\n`;
                    }
                    resultText += 'Content内容:\n' + qwen.content + '\n\n';
                }

                // 显示DeepSeek统计信息
                if (result.data.deepseek_response) {
                    const deepseek = result.data.deepseek_response;
                    resultText += '=== 🧠 DeepSeek-Chat 统计 ===\n';
                    resultText += `耗时: ${deepseek.duration_ms || '未知'}ms\n`;
                    if (deepseek.tokens) {
                        resultText += `Token消费: 总计${deepseek.tokens.total_tokens} (输入${deepseek.tokens.input_tokens} + 输出${deepseek.tokens.output_tokens})\n`;
                    }
                    resultText += 'Content内容:\n' + deepseek.content;
                }

                resultDiv.textContent = resultText;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ API调用失败\n\n错误信息: ' + (result.error || '未知错误');
            }
        }

        // 更新统计显示
        function updateStats(current, total, success, error, avgTime, tokens) {
            document.getElementById('currentTest').textContent = current;
            document.getElementById('totalTests').textContent = total;
            document.getElementById('successCount').textContent = success;
            document.getElementById('errorCount').textContent = error;
            document.getElementById('avgResponseTime').textContent = avgTime;
            document.getElementById('totalTokens').textContent = tokens;
        }

        // 生成最终统计报告
        function generateFinalStats(results, successCount, errorCount, totalResponseTime, totalTokens) {
            const totalTests = results.length;
            const successRate = totalTests > 0 ? ((successCount / totalTests) * 100).toFixed(1) : 0;
            const avgResponseTime = successCount > 0 ? Math.round(totalResponseTime / successCount) : 0;

            let stats = '\n=== 📊 连续测试统计报告 ===\n';
            stats += `总测试次数: ${totalTests}\n`;
            stats += `成功次数: ${successCount}\n`;
            stats += `失败次数: ${errorCount}\n`;
            stats += `成功率: ${successRate}%\n`;
            stats += `平均响应时间: ${avgResponseTime}ms\n`;
            stats += `总Token消耗: ${totalTokens}\n`;

            if (successCount > 0) {
                const responseTimes = results.filter(r => r.success).map(r => r.responseTime);
                const minTime = Math.min(...responseTimes);
                const maxTime = Math.max(...responseTimes);
                stats += `最快响应: ${minTime}ms\n`;
                stats += `最慢响应: ${maxTime}ms\n`;
            }

            return stats;
        }

        // 延时函数
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
    </script>
</body>
</html>
