{"timestamp": "2025-06-06 11:56:44", "changes": {"qwen.system_content": {"old": "你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目内容，包括题目类型、题干、选项信息。不需要解答题目，只需要完整准确地识别所有文字内容。返回json格式示例{\r\n  \"question_type\": \"单选题/多选题/判断题\",\r\n  \"question_text\": \"题干内容\",\r\n  \"options\": {\r\n    \"A\": \"选项内容\",\r\n    \"B\": \"选项内容\",\r\n    \"C\": \"判断题不存在此选项\",\r\n    \"D\": \"判断题不存在此选项\",\r\n  }\r\n}", "new": "你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目内容，包括题目类型、题干、选项信息。不需要解答题目，只需要完整准确地识别所有文字内容。返回json格式示例{\r\n  \"question_type\": \"单选题/多选题/判断题\",\r\n  \"question_text\": \"题干内容不包含题目序号\",\r\n  \"options\": {\r\n    \"A\": \"选项内容\",\r\n    \"B\": \"选项内容\",\r\n    \"C\": \"选项内容\",\r\n    \"D\": \"选项内容\",\r\n  }\r\n}"}}, "full_config": {"qwen": {"api_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation", "api_key": "sk-3920274bedf642c2b7495f534aadca84", "model": "qwen-vl-plus", "role": "system", "temperature": 0, "top_p": 0.1, "top_k": 1, "do_sample": false, "response_format": "json", "detail": "high", "system_content": "你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目内容，包括题目类型、题干、选项信息。不需要解答题目，只需要完整准确地识别所有文字内容。返回json格式示例{\r\n  \"question_type\": \"单选题/多选题/判断题\",\r\n  \"question_text\": \"题干内容不包含题目序号\",\r\n  \"options\": {\r\n    \"A\": \"选项内容\",\r\n    \"B\": \"选项内容\",\r\n    \"C\": \"选项内容\",\r\n    \"D\": \"选项内容\",\r\n  }\r\n}", "user_prompt_template": "如果识别到标点符号，统一转义为英文格式。不要识别换行，不要识别标题序号", "timeout": 60, "max_retries": 3}, "deepseek": {"api_url": "https://api.deepseek.com/chat/completions", "api_key": "***********************************", "model": "deepseek-chat", "role": "system", "temperature": 0, "max_tokens": 2000, "top_p": 0.1, "top_k": 50, "do_sample": true, "response_format": "text", "frequency_penalty": 0, "presence_penalty": 0, "system_content": "你是一个专业的驾照科目考试专家，具有丰富的交通法规知识和考试经验。请根据提供的题目内容，给出准确的答案和详细的解析。", "user_prompt_template": "请严格按照以下格式回复：\r\n\r\n题目类型：[单选题/多选题/判断题]\r\n题目内容：[问题的完整内容，不包含问题序号]\r\n选项内容：[A：完整选项内容；B：完整选项内容；C：完整选项内容；D：完整选项内容；]\r\n正确答案：[A,B,C,D] [如果是判断题则为Y或者N]\r\n答案解析：[答案解析内容]\r\n\r\n所有的标点符号都需要用英文符号。不可以出现多余的空格。", "timeout": 60, "max_retries": 3}, "system": {"log_file": "logs/api_logs.txt", "max_log_size": 10485760, "debug_mode": true, "verbose_logging": true, "api_timeout": 120}, "test": {"sample_image_urls": ["https://example.com/single-choice.jpg", "https://example.com/multiple-choice.jpg", "https://example.com/true-false.jpg"], "enable_test_mode": false}}, "change_count": 1}