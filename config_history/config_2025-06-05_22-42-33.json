{"timestamp": "2025-06-05 22:42:33", "changes": {"qwen.temperature": {"old": 0, "new": 0}, "qwen.user_prompt_template": {"old": "请按照以下格式输出：\r\n1. 题目类型：（单选题/多选题/判断题）\r\n2. 题目内容：（完整的题干）\r\n3. 选项内容：（所有选项的完整内容）\r\n4. 题干图片：（如果题目下方有图片，请描述图片内容）\r\n\r\n要求：\r\n- 完整准确地识别所有文字内容\r\n- 不要解答题目，只需要识别题目内容\r\n- 保持原有的格式和选项标识（A、B、C、D或Y、N）", "new": "始终返回如下 JSON 格式：{\\\\\\\"题目类型\\\\\\\": \\\\\\\"单选题\\/多选题\\/判断题\\\\\\\", \\\\\\\"题目内容\\\\\\\": \\\\\\\"题干文字\\\\\\\", \\\\\\\"选项内容\\\\\\\": {\\\\\\\"A\\\\\\\": \\\\\\\"...\\\\\\\", \\\\\\\"B\\\\\\\": \\\\\\\"...\\\\\\\", \\\\\\\"C\\\\\\\": \\\\\\\"...\\\\\\\", \\\\\\\"D\\\\\\\": \\\\\\\"...\\\\\\\"}, \\\\\\\"题干图片\\\\\\\": \\\\\\\"图片内容描述或留空\\\\\\\"}。所有字段都必须完整返回，即使某部分缺失，也请返回空字符串或空结构。"}, "deepseek.temperature": {"old": 0, "new": 0}, "deepseek.frequency_penalty": {"old": 0, "new": 0}}, "full_config": {"qwen": {"api_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation", "api_key": "sk-3920274bedf642c2b7495f534aadca84", "model": "qwen-vl-plus", "role": "system", "temperature": 0, "detail": "high", "system_content": "你是一个专业的图文专家，专门负责识别驾照考试题目。请精准识别图片中的考试题目内容，包括题目类型、题干、选项等信息。不需要解答题目，只需要完整准确地识别所有文字内容。", "user_prompt_template": "始终返回如下 JSON 格式：{\\\\\\\"题目类型\\\\\\\": \\\\\\\"单选题\\/多选题\\/判断题\\\\\\\", \\\\\\\"题目内容\\\\\\\": \\\\\\\"题干文字\\\\\\\", \\\\\\\"选项内容\\\\\\\": {\\\\\\\"A\\\\\\\": \\\\\\\"...\\\\\\\", \\\\\\\"B\\\\\\\": \\\\\\\"...\\\\\\\", \\\\\\\"C\\\\\\\": \\\\\\\"...\\\\\\\", \\\\\\\"D\\\\\\\": \\\\\\\"...\\\\\\\"}, \\\\\\\"题干图片\\\\\\\": \\\\\\\"图片内容描述或留空\\\\\\\"}。所有字段都必须完整返回，即使某部分缺失，也请返回空字符串或空结构。", "timeout": 60, "max_retries": 3}, "deepseek": {"api_url": "https://api.deepseek.com/chat/completions", "api_key": "***********************************", "model": "deepseek-chat", "role": "system", "temperature": 0, "max_tokens": 2000, "top_p": 0.1, "frequency_penalty": 0, "presence_penalty": 0, "system_content": "你是一个专业的驾照科目考试专家，具有丰富的交通法规知识和考试经验。请根据提供的题目内容，给出准确的答案和详细的解析。", "user_prompt_template": "请严格按照以下格式回复：\r\n\r\n题目类型：[单选题/多选题/判断题]\r\n题目内容：[问题的完整内容，不包含问题序号]\r\n选项内容：[A：完整选项内容；B：完整选项内容；C：完整选项内容；D：完整选项内容；]\r\n正确答案：[A,B,C,D] [如果是判断题则为Y或者N]\r\n答案解析：[答案解析内容]\r\n\r\n所有的标点符号都需要用英文符号。不可以出现多余的空格。", "timeout": 60, "max_retries": 3}, "system": {"log_file": "logs/api_logs.txt", "max_log_size": 10485760, "debug_mode": true, "verbose_logging": true, "api_timeout": 120}, "test": {"sample_image_urls": ["https://example.com/single-choice.jpg", "https://example.com/multiple-choice.jpg", "https://example.com/true-false.jpg"], "enable_test_mode": false}}, "change_count": 4}