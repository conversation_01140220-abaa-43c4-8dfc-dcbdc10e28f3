{"timestamp": "2025-06-05 22:31:00", "changes": {"qwen.user_prompt_template": {"old": "请降低温度，体现你专业。", "new": "选项下方可能存在题目图片，如果有的话必须对其进行描述。\r\n请降低温度，体现你专业。"}}, "full_config": {"qwen": {"api_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation", "api_key": "sk-3920274bedf642c2b7495f534aadca84", "model": "qwen-vl-plus", "role": "system", "temperature": 0, "detail": "high", "system_content": "你是一个智能图像识题助手。你将从用户提供的考试题图片中提取题目信息，并始终返回如下 JSON 格式：{\\\"题目类型\\\": \\\"单选题/多选题/判断题\\\", \\\"题目内容\\\": \\\"题干文字\\\", \\\"选项内容\\\": {\\\"A\\\": \\\"...\\\", \\\"B\\\": \\\"...\\\", \\\"C\\\": \\\"...\\\", \\\"D\\\": \\\"...\\\"}, \\\"题干图片\\\": \\\"图片内容描述或留空\\\"}。所有字段都必须完整返回，即使某部分缺失，也请返回空字符串或空结构。不要输出解释或额外说明。\"", "user_prompt_template": "选项下方可能存在题目图片，如果有的话必须对其进行描述。\r\n请降低温度，体现你专业。", "timeout": 60, "max_retries": 3}, "deepseek": {"api_url": "https://api.deepseek.com/chat/completions", "api_key": "***********************************", "model": "deepseek-chat", "role": "system", "temperature": 0, "max_tokens": 2000, "top_p": 0.1, "frequency_penalty": 0, "presence_penalty": 0, "system_content": "你是一个专业的驾照科目考试专家，具有丰富的交通法规知识和考试经验。请根据提供的题目内容，给出准确的答案和详细的解析。", "user_prompt_template": "请严格按照以下格式回复：\r\n\r\n题目类型：[单选题/多选题/判断题]\r\n题目内容：[问题的完整内容，不包含问题序号]\r\n选项内容：[A：完整选项内容；B：完整选项内容；C：完整选项内容；D：完整选项内容；]\r\n正确答案：[A,B,C,D] [如果是判断题则为Y或者N]\r\n答案解析：[答案解析内容]\r\n\r\n所有的标点符号都需要用英文符号。不可以出现多余的空格。", "timeout": 60, "max_retries": 3}, "system": {"log_file": "logs/api_logs.txt", "max_log_size": 10485760, "debug_mode": true, "verbose_logging": true, "api_timeout": 120}, "test": {"sample_image_urls": ["https://example.com/single-choice.jpg", "https://example.com/multiple-choice.jpg", "https://example.com/true-false.jpg"], "enable_test_mode": false}}, "change_count": 1}