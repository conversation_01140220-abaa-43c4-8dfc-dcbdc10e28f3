<?php
// 连续测试功能演示
echo "=== 🔄 连续测试功能演示 ===\n\n";

echo "本演示将展示连续测试功能的各种场景\n\n";

// 场景1: 快速验证测试
echo "📋 场景1: 快速验证测试 (3次，间隔1秒)\n";
echo "用途: 快速验证API功能是否正常\n";
echo "配置: 测试次数=3, 间隔=1秒, 遇错停止=开启\n\n";

runTestScenario("快速验证", 3, 1);

echo "\n" . str_repeat("=", 60) . "\n\n";

// 场景2: 性能评估测试
echo "📋 场景2: 性能评估测试 (5次，间隔2秒)\n";
echo "用途: 评估API性能和稳定性\n";
echo "配置: 测试次数=5, 间隔=2秒, 遇错停止=关闭\n\n";

runTestScenario("性能评估", 5, 2);

echo "\n" . str_repeat("=", 60) . "\n\n";

// 功能特性展示
echo "🎛️ 连续测试功能特性:\n";
echo "✅ 支持1-100次连续测试\n";
echo "✅ 可设置0-300秒间隔时间\n";
echo "✅ 实时进度显示和统计\n";
echo "✅ 支持手动停止测试\n";
echo "✅ 遇错停止选项\n";
echo "✅ 详细性能分析报告\n";
echo "✅ 保留单次测试功能\n\n";

echo "📊 统计信息包括:\n";
echo "• 当前测试进度\n";
echo "• 成功/失败次数\n";
echo "• 平均响应时间\n";
echo "• 总Token消耗\n";
echo "• 成功率分析\n";
echo "• 性能稳定性评估\n\n";

echo "🎯 适用场景:\n";
echo "• 🔬 性能测试: 评估响应时间和稳定性\n";
echo "• 🏋️ 压力测试: 测试高负载下的表现\n";
echo "• 🔍 稳定性测试: 验证长时间运行稳定性\n";
echo "• 💰 成本评估: 评估Token消耗和API成本\n\n";

echo "🚀 使用方法:\n";
echo "1. 访问 http://localhost:8080/test.html\n";
echo "2. 选择 '🔄 连续测试' 模式\n";
echo "3. 配置测试参数 (次数、间隔、遇错停止)\n";
echo "4. 输入图片URL\n";
echo "5. 点击 '🔄 开始连续测试'\n";
echo "6. 实时监控进度和统计\n";
echo "7. 查看最终性能报告\n\n";

echo "=== 演示完成 ===\n";
echo "🎉 连续测试功能已成功集成到API测试界面！\n";

function runTestScenario($scenarioName, $testCount, $intervalSeconds) {
    echo "开始执行 $scenarioName 测试...\n";
    
    $testImageUrl = "http://solve.igmdns.com/img/01.jpg";
    $successCount = 0;
    $errorCount = 0;
    $totalResponseTime = 0;
    $totalTokens = 0;
    
    for ($i = 1; $i <= $testCount; $i++) {
        echo "执行第 $i 次测试... ";
        
        $startTime = microtime(true);
        
        // 模拟API调用
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://localhost:8080/index.php');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['image_url' => $testImageUrl]));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        $endTime = microtime(true);
        $responseTime = ($endTime - $startTime) * 1000;
        
        if ($httpCode === 200 && $response) {
            $responseData = json_decode($response, true);
            
            if ($responseData && isset($responseData['success']) && $responseData['success']) {
                $successCount++;
                $totalResponseTime += $responseTime;
                
                // 计算Token
                $testTokens = 0;
                if (isset($responseData['qwen_response']['tokens'])) {
                    $testTokens += $responseData['qwen_response']['tokens']['total_tokens'];
                }
                if (isset($responseData['deepseek_response']['tokens'])) {
                    $testTokens += $responseData['deepseek_response']['tokens']['total_tokens'];
                }
                $totalTokens += $testTokens;
                
                echo "✅ 成功 (" . number_format($responseTime, 0) . "ms, {$testTokens} tokens)\n";
            } else {
                $errorCount++;
                echo "❌ API错误\n";
            }
        } else {
            $errorCount++;
            echo "❌ 网络错误 (HTTP $httpCode)\n";
        }
        
        // 等待间隔
        if ($i < $testCount && $intervalSeconds > 0) {
            sleep($intervalSeconds);
        }
    }
    
    // 输出统计
    echo "\n$scenarioName 测试结果:\n";
    echo "• 总测试次数: $testCount\n";
    echo "• 成功次数: $successCount\n";
    echo "• 失败次数: $errorCount\n";
    
    $successRate = $testCount > 0 ? ($successCount / $testCount) * 100 : 0;
    echo "• 成功率: " . number_format($successRate, 1) . "%\n";
    
    $avgResponseTime = $successCount > 0 ? $totalResponseTime / $successCount : 0;
    echo "• 平均响应时间: " . number_format($avgResponseTime, 0) . "ms\n";
    echo "• 总Token消耗: $totalTokens\n";
    
    // 性能评级
    if ($successRate == 100 && $avgResponseTime < 15000) {
        echo "• 性能评级: 🎉 优秀\n";
    } elseif ($successRate >= 90) {
        echo "• 性能评级: 👍 良好\n";
    } elseif ($successRate >= 70) {
        echo "• 性能评级: ⚠️ 一般\n";
    } else {
        echo "• 性能评级: ❌ 较差\n";
    }
}
?>
