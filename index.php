<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 加载配置
$config = require_once 'config.php';

// 确保日志目录存在
$logDir = dirname($config['system']['log_file']);
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

// 主要处理函数
function processExamImage($imageUrl, $config) {
    $result = [
        'success' => false,
        'qwen_response' => null,
        'deepseek_response' => null,
        'error' => null
    ];
    
    try {
        // 验证图片URL
        $validation = validateImageUrl($imageUrl);
        if (!$validation['valid']) {
            throw new Exception('图片URL验证失败: ' . $validation['error']);
        }

        // 步骤1: 调用qwen-vl-plus识别图片
        $qwenStartTime = microtime(true);
        $qwenResponse = callQwenAPI($imageUrl, $config['qwen']);
        $qwenEndTime = microtime(true);
        $qwenDuration = round(($qwenEndTime - $qwenStartTime) * 1000, 2); // 毫秒

        if (!$qwenResponse) {
            // 记录详细错误信息
            $errorMsg = 'Qwen API调用失败 - 图片URL: ' . $imageUrl . ' (可能原因: 图片无法访问、格式不支持或API服务问题)';
            error_log($errorMsg);
            throw new Exception($errorMsg);
        }

        // 提取token使用情况
        $qwenTokens = extractTokenUsage($qwenResponse, 'qwen');

        // 记录qwen日志（包含耗时、token信息和请求数据）
        $qwenRequestData = getQwenRequestData($imageUrl, $config['qwen']);
        logAPICall('qwen', $qwenResponse, $config['system']['log_file'], $qwenDuration, $qwenTokens, $qwenRequestData);

        // 提取qwen返回的内容
        $qwenContent = extractQwenContent($qwenResponse);

        // 步骤2: 检查是否启用DeepSeek，如果启用则调用DeepSeek API
        $deepseekResponse = null;
        $deepseekDuration = 0;
        $deepseekTokens = ['total_tokens' => 0, 'input_tokens' => 0, 'output_tokens' => 0];

        if (isset($config['deepseek']['enabled']) && $config['deepseek']['enabled']) {
            $deepseekStartTime = microtime(true);
            $deepseekResponse = callDeepseekAPI($qwenContent, $config['deepseek']);
            $deepseekEndTime = microtime(true);
            $deepseekDuration = round(($deepseekEndTime - $deepseekStartTime) * 1000, 2); // 毫秒

            if (!$deepseekResponse) {
                throw new Exception('DeepSeek API调用失败');
            }

            // 提取token使用情况
            $deepseekTokens = extractTokenUsage($deepseekResponse, 'deepseek');

            // 记录deepseek日志（包含耗时、token信息和请求数据）
            $deepseekRequestData = getDeepseekRequestData($qwenContent, $config['deepseek']);
            logAPICall('deepseek', $deepseekResponse, $config['system']['log_file'], $deepseekDuration, $deepseekTokens, $deepseekRequestData);
        } else {
            // DeepSeek 被禁用，记录日志
            logAPICall('info', 'DeepSeek API 已禁用，跳过调用', $config['system']['log_file']);
        }

        // 提取content字段的原始内容
        $qwenContent = '';
        if (isset($qwenResponse['output']['choices'][0]['message']['content'])) {
            $content = $qwenResponse['output']['choices'][0]['message']['content'];
            // 如果content是数组格式，提取text字段
            if (is_array($content) && isset($content[0]['text'])) {
                $qwenContent = $content[0]['text'];
            } else {
                $qwenContent = $content;
            }
        }

        $deepseekContent = '';
        if ($deepseekResponse && isset($deepseekResponse['choices'][0]['message']['content'])) {
            $deepseekContent = $deepseekResponse['choices'][0]['message']['content'];
        } elseif (!isset($config['deepseek']['enabled']) || !$config['deepseek']['enabled']) {
            $deepseekContent = 'DeepSeek API 已禁用';
        }

        // 返回给客户端的响应包含content原始内容和统计信息
        $result['qwen_response'] = [
            'content' => $qwenContent, // content字段原始内容
            'duration_ms' => $qwenDuration,
            'tokens' => $qwenTokens
        ];

        // 只有在 DeepSeek 启用时才返回 DeepSeek 响应
        if (isset($config['deepseek']['enabled']) && $config['deepseek']['enabled']) {
            $result['deepseek_response'] = [
                'content' => $deepseekContent, // content字段原始内容
                'duration_ms' => $deepseekDuration,
                'tokens' => $deepseekTokens
            ];
        } else {
            $result['deepseek_response'] = [
                'content' => 'DeepSeek API 已禁用，未调用',
                'duration_ms' => 0,
                'tokens' => ['total_tokens' => 0, 'input_tokens' => 0, 'output_tokens' => 0],
                'status' => 'disabled'
            ];
        }
        
        $result['success'] = true;
        
    } catch (Exception $e) {
        $result['error'] = $e->getMessage();
        logAPICall('error', $e->getMessage(), $config['system']['log_file']);
    }
    
    return $result;
}

// 验证图片URL
function validateImageUrl($imageUrl) {
    // 检查URL格式
    if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
        return ['valid' => false, 'error' => 'URL格式无效'];
    }

    // 检查协议（支持HTTP和HTTPS）
    if (!str_starts_with($imageUrl, 'http://') && !str_starts_with($imageUrl, 'https://')) {
        return ['valid' => false, 'error' => 'URL必须使用HTTP或HTTPS协议'];
    }

    // 检查文件扩展名（可选，因为有些URL可能没有扩展名）
    $path = parse_url($imageUrl, PHP_URL_PATH);
    if ($path) {
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        if ($extension) {
            $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
            if (!in_array($extension, $allowedExtensions)) {
                return ['valid' => false, 'error' => '不支持的图片格式，支持: ' . implode(', ', $allowedExtensions)];
            }
        }
    }

    return ['valid' => true, 'error' => null];
}

// 调用Qwen API
function callQwenAPI($imageUrl, $qwenConfig) {
    global $config;

    // 检查是否启用测试模式
    if (isset($config['test']['enable_test_mode']) && $config['test']['enable_test_mode']) {
        return [
            'output' => [
                'choices' => [
                    [
                        'message' => [
                            'content' => "1. 题目类型：单选题\n2. 题目内容：驾驶机动车驶近坡道顶端、视距不良时，应怎样做以确保安全？\n3. 选项内容：A、减速鸣喇叭示意 B、迅速行驶到坡顶以改善视距 C、长时间开启远光灯提醒对向来车 D、不间断鸣喇叭并加速冲坡\n4. 题干图片：无图片"
                        ]
                    ]
                ]
            ],
            'request_id' => 'test_' . uniqid(),
            'usage' => ['total_tokens' => 100]
        ];
    }

    // 验证图片URL
    $validation = validateImageUrl($imageUrl);
    if (!$validation['valid']) {
        error_log("图片URL验证失败: " . $validation['error'] . " URL: " . $imageUrl);
        return false;
    }
    $data = [
        'model' => $qwenConfig['model'],
        'input' => [
            'messages' => [
                [
                    'role' => $qwenConfig['role'],
                    'content' => $qwenConfig['system_content']
                ],
                [
                    'role' => 'user',
                    'content' => [
                        [
                            'image' => $imageUrl
                        ],
                        [
                            'text' => $qwenConfig['user_prompt_template']
                        ]
                    ]
                ]
            ]
        ],
        'parameters' => [
            'temperature' => $qwenConfig['temperature'],
            'top_p' => $qwenConfig['top_p'],
            'top_k' => $qwenConfig['top_k'],
            'do_sample' => $qwenConfig['do_sample'],
            'detail' => $qwenConfig['detail'],
            'frequency_penalty' => $qwenConfig['frequency_penalty'],
            'presence_penalty' => $qwenConfig['presence_penalty']
        ]
    ];

    // 添加 response_format 参数
    if ($qwenConfig['response_format'] === 'json') {
        $data['parameters']['response_format'] = ['type' => 'json_object'];
    } else {
        $data['parameters']['response_format'] = ['type' => 'text'];
    }

    return makeAPICall(
        $qwenConfig['api_url'],
        $data,
        [
            'Authorization: Bearer ' . $qwenConfig['api_key'],
            'Content-Type: application/json'
        ],
        $qwenConfig['timeout']
    );
}

// 调用DeepSeek API
function callDeepseekAPI($questionContent, $deepseekConfig) {
    global $config;

    // 检查是否启用 DeepSeek
    if (!isset($deepseekConfig['enabled']) || !$deepseekConfig['enabled']) {
        return null; // 返回 null 表示未启用
    }

    // 检查是否启用测试模式
    if (isset($config['test']['enable_test_mode']) && $config['test']['enable_test_mode']) {
        return [
            'choices' => [
                [
                    'message' => [
                        'content' => "题目类型：单选题\n题目内容：驾驶机动车驶近坡道顶端、视距不良时，应怎样做以确保安全？\n选项内容：A：减速鸣喇叭示意；B：迅速行驶到坡顶以改善视距；C：长时间开启远光灯提醒对向来车；D：不间断鸣喇叭并加速冲坡；\n正确答案：A\n答案解析：在坡道顶端视距不良时，应当减速并鸣喇叭示意，确保行车安全。"
                    ]
                ]
            ],
            'id' => 'test_' . uniqid(),
            'usage' => ['total_tokens' => 150]
        ];
    }
    $data = [
        'model' => $deepseekConfig['model'],
        'messages' => [
            [
                'role' => $deepseekConfig['role'],
                'content' => $deepseekConfig['system_content']
            ],
            [
                'role' => 'user',
                'content' => $questionContent . $deepseekConfig['user_prompt_template']
            ]
        ],
        'temperature' => $deepseekConfig['temperature'],
        'max_tokens' => $deepseekConfig['max_tokens'],
        'top_p' => $deepseekConfig['top_p'],
        'top_k' => $deepseekConfig['top_k'],
        'do_sample' => $deepseekConfig['do_sample'],
        'frequency_penalty' => $deepseekConfig['frequency_penalty'],
        'presence_penalty' => $deepseekConfig['presence_penalty']
    ];

    // 添加 response_format 参数（如果 DeepSeek 支持）
    if ($deepseekConfig['response_format'] === 'json') {
        $data['response_format'] = ['type' => 'json_object'];
    }

    return makeAPICall(
        $deepseekConfig['api_url'],
        $data,
        [
            'Authorization: Bearer ' . $deepseekConfig['api_key'],
            'Content-Type: application/json'
        ],
        $deepseekConfig['timeout']
    );
}

// 通用API调用函数
function makeAPICall($url, $data, $headers, $timeout = 60) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    if ($response === false) {
        $errorMsg = "cURL Error: " . $curlError . " URL: " . $url;
        error_log($errorMsg);
        logAPICall('curl_error', $errorMsg, $GLOBALS['config']['system']['log_file']);
        return false;
    }

    if ($httpCode !== 200) {
        $errorMsg = "HTTP Error: " . $httpCode . " Response: " . substr($response, 0, 500);
        error_log($errorMsg);
        logAPICall('http_error', $errorMsg, $GLOBALS['config']['system']['log_file']);
        return false;
    }

    return json_decode($response, true);
}

// 提取Qwen返回内容
function extractQwenContent($qwenResponse) {
    if (isset($qwenResponse['output']['choices'][0]['message']['content'])) {
        $content = $qwenResponse['output']['choices'][0]['message']['content'];

        // 处理新版API返回的数组格式
        if (is_array($content)) {
            $text = '';
            foreach ($content as $item) {
                if (isset($item['text'])) {
                    $text .= $item['text'];
                }
            }
            return $text;
        }

        // 处理旧版API返回的字符串格式
        return $content;
    }

    // 如果没有找到标准格式，返回整个响应用于调试
    return "API响应格式异常: " . json_encode($qwenResponse, JSON_UNESCAPED_UNICODE);
}

// 获取Qwen请求数据
function getQwenRequestData($imageUrl, $qwenConfig) {
    return [
        'model' => $qwenConfig['model'],
        'input' => [
            'messages' => [
                [
                    'role' => $qwenConfig['role'],
                    'content' => $qwenConfig['system_content']
                ],
                [
                    'role' => 'user',
                    'content' => [
                        [
                            'image' => $imageUrl
                        ],
                        [
                            'text' => $qwenConfig['user_prompt_template']
                        ]
                    ]
                ]
            ]
        ],
        'parameters' => [
            'temperature' => $qwenConfig['temperature'],
            'top_p' => $qwenConfig['top_p'],
            'top_k' => $qwenConfig['top_k'],
            'do_sample' => $qwenConfig['do_sample'],
            'response_format' => $qwenConfig['response_format'] === 'json'
                ? ['type' => 'json_object']
                : ['type' => 'text'],
            'detail' => $qwenConfig['detail'],
            'frequency_penalty' => $qwenConfig['frequency_penalty'],
            'presence_penalty' => $qwenConfig['presence_penalty']
        ]
    ];
}

// 获取DeepSeek请求数据
function getDeepseekRequestData($questionContent, $deepseekConfig) {
    $data = [
        'model' => $deepseekConfig['model'],
        'messages' => [
            [
                'role' => $deepseekConfig['role'],
                'content' => $deepseekConfig['system_content']
            ],
            [
                'role' => 'user',
                'content' => $questionContent . $deepseekConfig['user_prompt_template']
            ]
        ],
        'temperature' => $deepseekConfig['temperature'],
        'max_tokens' => $deepseekConfig['max_tokens'],
        'top_p' => $deepseekConfig['top_p'],
        'top_k' => $deepseekConfig['top_k'],
        'do_sample' => $deepseekConfig['do_sample'],
        'frequency_penalty' => $deepseekConfig['frequency_penalty'],
        'presence_penalty' => $deepseekConfig['presence_penalty']
    ];

    // 添加 response_format 参数（如果配置为 json）
    if ($deepseekConfig['response_format'] === 'json') {
        $data['response_format'] = ['type' => 'json_object'];
    }

    return $data;
}

// 提取Token使用情况
function extractTokenUsage($response, $apiType) {
    $tokens = [
        'total_tokens' => 0,
        'input_tokens' => 0,
        'output_tokens' => 0
    ];

    if ($apiType === 'qwen') {
        if (isset($response['usage'])) {
            $usage = $response['usage'];
            $tokens['total_tokens'] = $usage['total_tokens'] ?? 0;
            $tokens['input_tokens'] = $usage['input_tokens'] ?? 0;
            $tokens['output_tokens'] = $usage['output_tokens'] ?? 0;
        }
    } elseif ($apiType === 'deepseek') {
        if (isset($response['usage'])) {
            $usage = $response['usage'];
            $tokens['total_tokens'] = $usage['total_tokens'] ?? 0;
            $tokens['input_tokens'] = $usage['prompt_tokens'] ?? 0;
            $tokens['output_tokens'] = $usage['completion_tokens'] ?? 0;
        }
    }

    return $tokens;
}

// 记录API调用日志
function logAPICall($type, $data, $logFile, $duration = null, $tokens = null, $requestData = null) {
    $timestamp = date('Y-m-d H:i:s');

    // 根据类型确定日志文件和内容
    if ($type === 'qwen') {
        $specificLogFile = str_replace('.txt', '_qwen.txt', $logFile);
        // 提取content字段的原始内容
        if (isset($data['output']['choices'][0]['message']['content'])) {
            $content = $data['output']['choices'][0]['message']['content'];
            // 如果content是数组格式，提取text字段
            if (is_array($content) && isset($content[0]['text'])) {
                $content = $content[0]['text'];
            }
        } else {
            $content = json_encode($data, JSON_UNESCAPED_UNICODE);
        }

        // 第一行：时间戳和content原始内容（转义换行符用于日志显示）
        $logEntry = "[$timestamp] qwen: " . str_replace(["\n", "\r"], ["\\n", "\\r"], $content) . "\n";
        // 第二行：统计信息
        $statsLine = "[$timestamp] 耗时: " . ($duration ? $duration . "ms" : "未知");
        if ($tokens) {
            $statsLine .= ", Token消费: 总计{$tokens['total_tokens']} (输入{$tokens['input_tokens']} + 输出{$tokens['output_tokens']})";
        }
        $statsLine .= "\n";
        $logEntry .= $statsLine;

        // 请求数据单独记录到请求日志文件
        if ($requestData) {
            $requestLogFile = str_replace('.txt', '_qwen_request.txt', $logFile);
            $requestLine = "[$timestamp] qwen_request: " . json_encode($requestData, JSON_UNESCAPED_UNICODE) . "\n";
            file_put_contents($requestLogFile, $requestLine, FILE_APPEND | LOCK_EX);
        }

    } elseif ($type === 'deepseek') {
        $specificLogFile = str_replace('.txt', '_deepseek.txt', $logFile);
        // 提取content字段的原始内容
        $content = isset($data['choices'][0]['message']['content'])
            ? $data['choices'][0]['message']['content']
            : json_encode($data, JSON_UNESCAPED_UNICODE);

        // 第一行：时间戳和content原始内容（转义换行符用于日志显示）
        $logEntry = "[$timestamp] deepseek: " . str_replace(["\n", "\r"], ["\\n", "\\r"], $content) . "\n";
        // 第二行：统计信息
        $statsLine = "[$timestamp] 耗时: " . ($duration ? $duration . "ms" : "未知");
        if ($tokens) {
            $statsLine .= ", Token消费: 总计{$tokens['total_tokens']} (输入{$tokens['input_tokens']} + 输出{$tokens['output_tokens']})";
        }
        $statsLine .= "\n";
        $logEntry .= $statsLine;

        // 请求数据单独记录到请求日志文件
        if ($requestData) {
            $requestLogFile = str_replace('.txt', '_deepseek_request.txt', $logFile);
            $requestLine = "[$timestamp] deepseek_request: " . json_encode($requestData, JSON_UNESCAPED_UNICODE) . "\n";
            file_put_contents($requestLogFile, $requestLine, FILE_APPEND | LOCK_EX);
        }

    } else {
        // 其他类型（error、curl_error、http_error等）记录到通用日志
        $specificLogFile = $logFile;
        $logContent = is_string($data) ? $data : json_encode($data, JSON_UNESCAPED_UNICODE);
        $logEntry = "[$timestamp] $type: " . $logContent . "\n";
    }

    // 写入对应的日志文件
    file_put_contents($specificLogFile, $logEntry, FILE_APPEND | LOCK_EX);

    // 同时写入通用日志文件（用于统一查看）
    if ($type === 'qwen' || $type === 'deepseek') {
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
}

// 处理请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['image_url']) || empty($input['image_url'])) {
        http_response_code(400);
        echo json_encode(['error' => '缺少image_url参数']);
        exit();
    }
    
    $result = processExamImage($input['image_url'], $config);
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
    
} elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
    // 显示使用说明
    echo json_encode([
        'message' => '驾照考试题目识别API',
        'usage' => [
            'method' => 'POST',
            'content_type' => 'application/json',
            'body' => [
                'image_url' => '图片URL地址'
            ]
        ],
        'curl_example' => 'curl -X POST -H "Content-Type: application/json" -d \'{"image_url":"https://example.com/image.jpg"}\' http://your-domain.com/index.php'
    ], JSON_UNESCAPED_UNICODE);
    
} else {
    http_response_code(405);
    echo json_encode(['error' => '不支持的请求方法']);
}
?>
