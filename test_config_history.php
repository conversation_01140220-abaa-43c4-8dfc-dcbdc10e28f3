<?php
// 测试配置历史记录功能
require_once 'config_manager.php';

echo "=== 测试配置历史记录功能 ===\n\n";

// 加载当前配置
$currentConfig = require 'config.php';

// 模拟配置变更
$newConfig = $currentConfig;
$newConfig['qwen']['temperature'] = 0.1; // 从0改为0.1
$newConfig['deepseek']['temperature'] = 0.2; // 从0改为0.2
$newConfig['system']['debug_mode'] = true; // 改为true

echo "1. 模拟配置变更...\n";
echo "   - Qwen温度: 0 → 0.1\n";
echo "   - DeepSeek温度: 0 → 0.2\n";
echo "   - 调试模式: false → true\n\n";

// 保存配置历史
echo "2. 保存配置历史...\n";
saveConfigHistory($currentConfig, $newConfig);
echo "   ✅ 配置历史已保存\n\n";

// 检查历史文件
echo "3. 检查生成的文件...\n";
$historyDir = 'config_history';
if (is_dir($historyDir)) {
    $files = scandir($historyDir);
    $configFiles = array_filter($files, function($file) {
        return strpos($file, 'config_') === 0 && pathinfo($file, PATHINFO_EXTENSION) === 'json';
    });
    
    echo "   历史文件数量: " . count($configFiles) . "\n";
    
    if (!empty($configFiles)) {
        $latestFile = end($configFiles);
        echo "   最新文件: $latestFile\n";
        
        $historyData = json_decode(file_get_contents($historyDir . '/' . $latestFile), true);
        echo "   变更数量: " . count($historyData['changes']) . "\n";
        echo "   时间戳: " . $historyData['timestamp'] . "\n";
    }
}

// 检查索引文件
echo "\n4. 检查索引文件...\n";
$indexFile = 'config_history/index.json';
if (file_exists($indexFile)) {
    $index = json_decode(file_get_contents($indexFile), true);
    echo "   索引记录数: " . count($index) . "\n";
    if (!empty($index)) {
        $latest = end($index);
        echo "   最新记录: " . $latest['timestamp'] . " (变更" . $latest['change_count'] . "项)\n";
    }
} else {
    echo "   ❌ 索引文件不存在\n";
}

echo "\n=== 测试完成 ===\n";
echo "您可以访问 config_history_viewer.php 查看配置历史记录\n";
?>
