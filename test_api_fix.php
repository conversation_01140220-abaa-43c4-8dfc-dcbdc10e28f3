<?php
// 测试修复后的 API 功能
echo "=== 测试修复后的 API 功能 ===\n\n";

// 模拟 API 请求
$testImageUrl = "http://solve.igmdns.com/img/01.jpg";

echo "1. 测试图片 URL: $testImageUrl\n";

// 构建请求数据
$requestData = [
    'image_url' => $testImageUrl
];

// 发送 POST 请求到 API
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8080/index.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 120);

$startTime = microtime(true);
$response = curl_exec($ch);
$endTime = microtime(true);

$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

$responseTime = ($endTime - $startTime) * 1000;

echo "\n2. API 调用结果:\n";
echo "   HTTP 状态码: $httpCode\n";
echo "   响应时间: " . number_format($responseTime, 2) . "ms\n";

if ($curlError) {
    echo "   ❌ CURL 错误: $curlError\n";
    exit(1);
}

if ($httpCode !== 200) {
    echo "   ❌ HTTP 错误: $httpCode\n";
    echo "   响应内容: $response\n";
    exit(1);
}

echo "   ✅ API 调用成功\n";

// 解析响应
$responseData = json_decode($response, true);

if ($responseData === null) {
    echo "   ❌ 响应不是有效的 JSON\n";
    echo "   原始响应: $response\n";
    exit(1);
}

echo "   ✅ 响应是有效的 JSON\n";

// 检查响应结构
if (isset($responseData['success']) && $responseData['success']) {
    echo "   ✅ API 返回成功状态\n";
    
    if (isset($responseData['qwen_result'])) {
        echo "   ✅ 包含 Qwen 结果\n";
        echo "   Qwen 内容长度: " . strlen($responseData['qwen_result']) . " 字符\n";
        
        // 显示 Qwen 结果的前200字符
        $qwenPreview = substr($responseData['qwen_result'], 0, 200);
        echo "   Qwen 内容预览: " . $qwenPreview . "...\n";
    }
    
    if (isset($responseData['deepseek_result'])) {
        echo "   ✅ 包含 DeepSeek 结果\n";
        echo "   DeepSeek 内容长度: " . strlen($responseData['deepseek_result']) . " 字符\n";
        
        // 显示 DeepSeek 结果的前200字符
        $deepseekPreview = substr($responseData['deepseek_result'], 0, 200);
        echo "   DeepSeek 内容预览: " . $deepseekPreview . "...\n";
    }
    
    if (isset($responseData['qwen_tokens'])) {
        $tokens = $responseData['qwen_tokens'];
        echo "   ✅ Qwen Token 使用: 总计{$tokens['total_tokens']} (输入{$tokens['input_tokens']} + 输出{$tokens['output_tokens']})\n";
    }
    
    if (isset($responseData['deepseek_tokens'])) {
        $tokens = $responseData['deepseek_tokens'];
        echo "   ✅ DeepSeek Token 使用: 总计{$tokens['total_tokens']} (输入{$tokens['input_tokens']} + 输出{$tokens['output_tokens']})\n";
    }
    
    if (isset($responseData['qwen_duration'])) {
        echo "   ✅ Qwen 响应时间: " . number_format($responseData['qwen_duration'], 2) . "ms\n";
    }
    
    if (isset($responseData['deepseek_duration'])) {
        echo "   ✅ DeepSeek 响应时间: " . number_format($responseData['deepseek_duration'], 2) . "ms\n";
    }
    
} else {
    echo "   ❌ API 返回失败状态\n";
    
    if (isset($responseData['error'])) {
        echo "   错误信息: " . $responseData['error'] . "\n";
    }
    
    if (isset($responseData['qwen_error'])) {
        echo "   Qwen 错误: " . $responseData['qwen_error'] . "\n";
    }
    
    if (isset($responseData['deepseek_error'])) {
        echo "   DeepSeek 错误: " . $responseData['deepseek_error'] . "\n";
    }
}

echo "\n3. 完整响应数据:\n";
echo json_encode($responseData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";

echo "\n=== 测试完成 ===\n";

// 检查日志文件
echo "\n4. 检查日志文件:\n";
$logFiles = [
    'logs/api_logs.txt',
    'logs/api_logs_qwen.txt',
    'logs/api_logs_deepseek.txt'
];

foreach ($logFiles as $logFile) {
    if (file_exists($logFile)) {
        $size = filesize($logFile);
        $lines = count(file($logFile));
        echo "   ✅ $logFile: $size 字节, $lines 行\n";
    } else {
        echo "   ❌ $logFile: 文件不存在\n";
    }
}

echo "\n✅ API 修复测试完成！\n";
