<?php
// API配置文件
// 请在此处填入您的API密钥和调整技术参数

return array (
  'qwen' => 
  array (
    'api_url' => 'https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation',
    'api_key' => 'sk-3920274bedf642c2b7495f534aadca84',
    'model' => 'qwen-vl-plus',
    'role' => 'system',
    'temperature' => 0.0,
    'top_p' => 0.01,
    'top_k' => 1,
    'do_sample' => false,
    'response_format' => 'json',
    'detail' => 'high',
    'frequency_penalty' => -2.0,
    'presence_penalty' => -2.0,
    'system_content' => '精准识别问提号与内容。严格标准返回json格式示例{"question_type": "单选题or多选题or判断题""question_text": "完整的问题","options": {"A": "选项内容","B": "选项内容","C": "选项内容","D": "选项内容",}}',
    'user_prompt_template' => '',
    'timeout' => 60,
    'max_retries' => 3,
  ),
  'deepseek' => 
  array (
    'enabled' => false,
    'api_url' => 'https://api.deepseek.com/chat/completions',
    'api_key' => 'sk-dd3347aa018244b1a2e19bb364c3c97e',
    'model' => 'deepseek-chat',
    'role' => 'system',
    'temperature' => 0.0,
    'max_tokens' => 2000,
    'top_p' => 0.1,
    'top_k' => 50,
    'do_sample' => true,
    'response_format' => 'text',
    'frequency_penalty' => 0.0,
    'presence_penalty' => 0.0,
    'system_content' => '你是一个专业的驾照科目考试专家，具有丰富的交通法规知识和考试经验。请根据提供的题目内容，给出准确的答案和详细的解析。',
    'user_prompt_template' => '请严格按照以下格式回复：

题目类型：[单选题/多选题/判断题]
题目内容：[问题的完整内容，不包含问题序号]
选项内容：[A：完整选项内容；B：完整选项内容；C：完整选项内容；D：完整选项内容；]
正确答案：[A,B,C,D] [如果是判断题则为Y或者N]
答案解析：[答案解析内容]

所有的标点符号都需要用英文符号。不可以出现多余的空格。',
    'timeout' => 60,
    'max_retries' => 3,
  ),
  'system' => 
  array (
    'log_file' => 'logs/api_logs.txt',
    'max_log_size' => 10485760,
    'debug_mode' => true,
    'verbose_logging' => true,
    'api_timeout' => 120,
  ),
  'test' => 
  array (
    'sample_image_urls' => 
    array (
      0 => 'https://example.com/single-choice.jpg',
      1 => 'https://example.com/multiple-choice.jpg',
      2 => 'https://example.com/true-false.jpg',
    ),
    'enable_test_mode' => false,
  ),
);
