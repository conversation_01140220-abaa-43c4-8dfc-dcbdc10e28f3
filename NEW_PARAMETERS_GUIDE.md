# 🎛️ 新增参数控制指南

## 📋 概述

系统已成功增加了对 `top_p`、`top_k`、`do_sample` 三个重要参数的控制，这些参数可以精细调节 AI 模型的输出行为。

## 🔧 新增参数详解

### 1. **Top P (核采样)**
- **范围**: 0.01 - 1.0
- **默认值**: 
  - Qwen: 0.8
  - DeepSeek: 0.1
- **作用**: 控制候选词汇的累积概率阈值
- **效果**:
  - 较小值 (0.1-0.3): 输出更保守、一致性更高
  - 较大值 (0.7-0.9): 输出更多样化、创造性更强

### 2. **Top K (候选词数量)**
- **范围**: 1 - 100
- **默认值**: 50 (两个模型相同)
- **作用**: 保留概率最高的 K 个候选词汇
- **效果**:
  - 较小值 (1-20): 输出更确定、重复性更高
  - 较大值 (50-100): 输出更丰富、词汇选择更广

### 3. **Do Sample (采样模式)**
- **类型**: 布尔值 (true/false)
- **默认值**: true (两个模型相同)
- **作用**: 控制是否启用随机采样
- **效果**:
  - `false`: 贪婪搜索，输出确定性最高
  - `true`: 随机采样，结合 temperature、top_p、top_k 控制随机性

## 🎯 预设配置模式

### 严格模式 (Strict)
```
temperature: 0
top_p: 1.0
top_k: 1
do_sample: false
```
- **特点**: 最确定性的输出
- **适用**: 需要一致性答案的场景

### 创意模式 (Creative)
```
temperature: 0.3
top_p: 0.9
top_k: 80
do_sample: true
```
- **特点**: 高创造性和多样性
- **适用**: 需要创新思路的场景

### 平衡模式 (Balanced)
```
temperature: 0.1
top_p: 0.95
top_k: 50
do_sample: true
```
- **特点**: 平衡确定性和创造性
- **适用**: 大多数常规应用场景

## 🛠️ 使用方法

### 1. **通过配置管理器**
1. 访问 `http://localhost:8080/config_manager.php`
2. 在 Qwen 或 DeepSeek 配置区域找到新增的参数控制
3. 调整参数值
4. 点击"保存配置"

### 2. **使用预设模式**
1. 在配置管理器页面顶部找到"预设配置"按钮
2. 选择合适的模式：
   - 严格模式：确定性输出
   - 创意模式：多样化输出
   - 平衡模式：中等随机性
   - 调试模式：启用详细日志

### 3. **直接编辑配置文件**
```php
'qwen' => [
    'temperature' => 0.1,
    'top_p' => 0.8,
    'top_k' => 50,
    'do_sample' => true,
    // ... 其他参数
],
'deepseek' => [
    'temperature' => 0.1,
    'top_p' => 0.8,
    'top_k' => 50,
    'do_sample' => true,
    // ... 其他参数
]
```

## 📊 参数组合建议

### 高质量文本识别 (推荐)
```
temperature: 0
top_p: 0.9
top_k: 30
do_sample: true
```

### 创意内容生成
```
temperature: 0.3
top_p: 0.8
top_k: 80
do_sample: true
```

### 精确任务执行
```
temperature: 0
top_p: 1.0
top_k: 1
do_sample: false
```

## 🔍 参数影响分析

### Temperature vs Top P vs Top K
- **Temperature**: 控制整体随机性强度
- **Top P**: 动态调整候选词范围
- **Top K**: 固定候选词数量上限
- **Do Sample**: 决定是否启用随机性

### 最佳实践
1. **先设置 do_sample**: 决定基本策略
2. **调整 temperature**: 控制随机性强度
3. **配置 top_p**: 精细控制候选范围
4. **设置 top_k**: 限制最大候选数

## 🧪 测试建议

### 1. **功能验证**
```bash
php test_new_parameters.php
```

### 2. **实际测试**
1. 使用不同参数组合
2. 通过 `test.html` 页面测试 API
3. 观察输出差异
4. 查看日志记录

### 3. **性能监控**
- 检查响应时间变化
- 监控 Token 消耗
- 观察输出质量

## 📈 效果对比

| 参数组合 | 确定性 | 创造性 | 一致性 | 适用场景 |
|---------|--------|--------|--------|----------|
| 严格模式 | ⭐⭐⭐⭐⭐ | ⭐ | ⭐⭐⭐⭐⭐ | 标准化任务 |
| 平衡模式 | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ | 通用场景 |
| 创意模式 | ⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐ | 创新任务 |

## 🔧 故障排除

### 常见问题
1. **参数不生效**: 检查配置文件格式
2. **输出异常**: 验证参数值范围
3. **性能下降**: 调整 top_k 值

### 调试方法
1. 启用调试模式
2. 查看请求日志
3. 对比不同参数效果

## 📚 技术细节

### API 集成
- Qwen API: 参数在 `parameters` 字段中传递
- DeepSeek API: 参数在请求根级别传递
- 自动类型转换: 确保参数类型正确

### 配置管理
- 实时保存配置变更
- 自动生成历史记录
- 参数验证和范围检查

---

**🎉 新参数功能已完全集成到系统中，可以通过配置管理器轻松控制 AI 模型的输出行为！**
