# 驾照考试题目识别API - Apache配置

# 启用重写引擎
RewriteEngine On

# 安全设置
# 禁止访问敏感文件
<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

# 设置安全头
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# CORS设置（如果需要跨域访问）
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "POST, GET, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type"

# 处理OPTIONS预检请求
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ index.php [QSA,L]

# API路由重写
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/?$ index.php [QSA,L]

# 日志查看器路由
RewriteRule ^logs/?$ logs/view_logs.php [QSA,L]

# 错误页面
ErrorDocument 404 /index.php
ErrorDocument 500 /index.php

# 缓存设置
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/html "access plus 1 hour"
    ExpiresByType application/json "access plus 1 minute"
</IfModule>

# 压缩设置
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/json
</IfModule>
