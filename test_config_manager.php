<?php
// 测试配置管理器功能
echo "=== 配置管理器功能测试 ===\n\n";

// 1. 测试配置文件加载
echo "1. 测试配置文件加载...\n";
try {
    $config = require 'config.php';
    echo "   ✅ 配置文件加载成功\n";
    echo "   - Qwen模型: " . $config['qwen']['model'] . "\n";
    echo "   - DeepSeek模型: " . $config['deepseek']['model'] . "\n";
    echo "   - 测试模式: " . ($config['test']['enable_test_mode'] ? '开启' : '关闭') . "\n";
} catch (Exception $e) {
    echo "   ❌ 配置文件加载失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 2. 检查必需的配置字段
echo "\n2. 检查配置字段完整性...\n";
$requiredFields = [
    'qwen.model', 'qwen.role', 'qwen.temperature', 'qwen.detail', 'qwen.timeout', 'qwen.max_retries',
    'deepseek.model', 'deepseek.role', 'deepseek.temperature', 'deepseek.max_tokens', 'deepseek.top_p', 
    'deepseek.timeout', 'deepseek.max_retries',
    'system.debug_mode', 'system.verbose_logging', 'system.api_timeout',
    'test.enable_test_mode'
];

$missingFields = [];
foreach ($requiredFields as $field) {
    $keys = explode('.', $field);
    $value = $config;
    $exists = true;
    
    foreach ($keys as $key) {
        if (!isset($value[$key])) {
            $exists = false;
            break;
        }
        $value = $value[$key];
    }
    
    if (!$exists) {
        $missingFields[] = $field;
    }
}

if (empty($missingFields)) {
    echo "   ✅ 所有必需字段都存在\n";
} else {
    echo "   ❌ 缺少字段: " . implode(', ', $missingFields) . "\n";
}

// 3. 检查数据类型
echo "\n3. 检查数据类型...\n";
$typeChecks = [
    ['qwen.temperature', 'float', $config['qwen']['temperature']],
    ['deepseek.temperature', 'float', $config['deepseek']['temperature']],
    ['deepseek.top_p', 'float', $config['deepseek']['top_p']],
    ['deepseek.max_tokens', 'int', $config['deepseek']['max_tokens']],
    ['system.debug_mode', 'bool', $config['system']['debug_mode']],
    ['test.enable_test_mode', 'bool', $config['test']['enable_test_mode']]
];

foreach ($typeChecks as [$field, $expectedType, $value]) {
    $actualType = gettype($value);
    $typeMap = ['double' => 'float', 'integer' => 'int', 'boolean' => 'bool'];
    $actualType = $typeMap[$actualType] ?? $actualType;
    
    if ($actualType === $expectedType) {
        echo "   ✅ $field: $expectedType ($value)\n";
    } else {
        echo "   ❌ $field: 期望$expectedType，实际$actualType ($value)\n";
    }
}

// 4. 检查数值范围
echo "\n4. 检查数值范围...\n";
$rangeChecks = [
    ['qwen.temperature', $config['qwen']['temperature'], 0, 1],
    ['deepseek.temperature', $config['deepseek']['temperature'], 0, 1],
    ['deepseek.top_p', $config['deepseek']['top_p'], 0.01, 1],
    ['deepseek.max_tokens', $config['deepseek']['max_tokens'], 100, 8000]
];

foreach ($rangeChecks as [$field, $value, $min, $max]) {
    if ($value >= $min && $value <= $max) {
        echo "   ✅ $field: $value (范围: $min-$max)\n";
    } else {
        echo "   ❌ $field: $value 超出范围 $min-$max\n";
    }
}

// 5. 模拟配置更新测试
echo "\n5. 模拟配置更新测试...\n";
$testConfig = $config;
$testConfig['qwen']['temperature'] = 0.2;
$testConfig['deepseek']['temperature'] = 0.3;
$testConfig['system']['debug_mode'] = false;

// 模拟配置管理器的处理逻辑
$newConfig = $config;

// 模拟POST数据
$_POST = [
    'action' => 'update_config',
    'qwen' => ['temperature' => '0.2'],
    'deepseek' => ['temperature' => '0.3'],
    'system' => ['debug_mode' => 'false']
];

// 应用更新逻辑
if (isset($_POST['qwen'])) {
    foreach ($_POST['qwen'] as $key => $value) {
        if ($key === 'temperature') {
            $newConfig['qwen'][$key] = (float)$value;
        }
    }
}

if (isset($_POST['deepseek'])) {
    foreach ($_POST['deepseek'] as $key => $value) {
        if ($key === 'temperature') {
            $newConfig['deepseek'][$key] = (float)$value;
        }
    }
}

if (isset($_POST['system'])) {
    foreach ($_POST['system'] as $key => $value) {
        if ($key === 'debug_mode') {
            $newConfig['system'][$key] = $value === 'true';
        }
    }
}

// 验证更新结果
if ($newConfig['qwen']['temperature'] === 0.2) {
    echo "   ✅ Qwen温度更新正确: 0.2\n";
} else {
    echo "   ❌ Qwen温度更新失败: " . $newConfig['qwen']['temperature'] . "\n";
}

if ($newConfig['deepseek']['temperature'] === 0.3) {
    echo "   ✅ DeepSeek温度更新正确: 0.3\n";
} else {
    echo "   ❌ DeepSeek温度更新失败: " . $newConfig['deepseek']['temperature'] . "\n";
}

if ($newConfig['system']['debug_mode'] === false) {
    echo "   ✅ 调试模式更新正确: false\n";
} else {
    echo "   ❌ 调试模式更新失败: " . ($newConfig['system']['debug_mode'] ? 'true' : 'false') . "\n";
}

// 6. 检查配置管理器文件
echo "\n6. 检查配置管理器文件...\n";
if (file_exists('config_manager.php')) {
    echo "   ✅ config_manager.php 存在\n";
    
    // 检查文件是否可读
    if (is_readable('config_manager.php')) {
        echo "   ✅ config_manager.php 可读\n";
    } else {
        echo "   ❌ config_manager.php 不可读\n";
    }
} else {
    echo "   ❌ config_manager.php 不存在\n";
}

echo "\n=== 测试完成 ===\n";

// 总结
$issues = [];
if (!empty($missingFields)) {
    $issues[] = "缺少配置字段";
}

if (empty($issues)) {
    echo "🎉 配置管理器功能正常！\n";
    echo "您可以访问 config_manager.php 进行配置管理。\n";
} else {
    echo "⚠️ 发现问题: " . implode(', ', $issues) . "\n";
    echo "请检查并修复这些问题。\n";
}
?>
