<?php
// 测试API连接脚本
$config = require_once 'config.php';

echo "=== API连接测试 ===\n\n";

// 测试配置
echo "1. 检查配置文件...\n";
echo "Qwen API Key: " . (empty($config['qwen']['api_key']) ? "❌ 未配置" : "✅ 已配置") . "\n";
echo "DeepSeek API Key: " . (empty($config['deepseek']['api_key']) ? "❌ 未配置" : "✅ 已配置") . "\n";
echo "Qwen Model: " . $config['qwen']['model'] . "\n";
echo "DeepSeek Model: " . $config['deepseek']['model'] . "\n\n";

// 测试Qwen API连接
echo "2. 测试Qwen API连接...\n";
$qwenTestData = [
    'model' => $config['qwen']['model'],
    'input' => [
        'messages' => [
            [
                'role' => 'user',
                'content' => [
                    [
                        'text' => '测试连接'
                    ]
                ]
            ]
        ]
    ],
    'parameters' => [
        'temperature' => 0
    ]
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $config['qwen']['api_url']);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($qwenTestData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $config['qwen']['api_key'],
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$qwenResponse = curl_exec($ch);
$qwenHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$qwenError = curl_error($ch);
curl_close($ch);

echo "Qwen HTTP状态码: $qwenHttpCode\n";
if ($qwenError) {
    echo "Qwen cURL错误: $qwenError\n";
}
if ($qwenResponse) {
    $qwenData = json_decode($qwenResponse, true);
    if ($qwenData) {
        echo "Qwen API响应: ✅ 连接成功\n";
        if (isset($qwenData['output'])) {
            echo "Qwen 返回数据结构正常\n";
        } elseif (isset($qwenData['error'])) {
            echo "Qwen API错误: " . $qwenData['error']['message'] . "\n";
        }
    } else {
        echo "Qwen JSON解析失败\n";
        echo "原始响应: " . substr($qwenResponse, 0, 200) . "\n";
    }
} else {
    echo "Qwen API无响应\n";
}
echo "\n";

// 测试DeepSeek API连接
echo "3. 测试DeepSeek API连接...\n";
$deepseekTestData = [
    'model' => $config['deepseek']['model'],
    'messages' => [
        [
            'role' => 'user',
            'content' => '测试连接'
        ]
    ],
    'temperature' => 0
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $config['deepseek']['api_url']);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($deepseekTestData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $config['deepseek']['api_key'],
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$deepseekResponse = curl_exec($ch);
$deepseekHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$deepseekError = curl_error($ch);
curl_close($ch);

echo "DeepSeek HTTP状态码: $deepseekHttpCode\n";
if ($deepseekError) {
    echo "DeepSeek cURL错误: $deepseekError\n";
}
if ($deepseekResponse) {
    $deepseekData = json_decode($deepseekResponse, true);
    if ($deepseekData) {
        echo "DeepSeek API响应: ✅ 连接成功\n";
        if (isset($deepseekData['choices'])) {
            echo "DeepSeek 返回数据结构正常\n";
        } elseif (isset($deepseekData['error'])) {
            echo "DeepSeek API错误: " . $deepseekData['error']['message'] . "\n";
        }
    } else {
        echo "DeepSeek JSON解析失败\n";
        echo "原始响应: " . substr($deepseekResponse, 0, 200) . "\n";
    }
} else {
    echo "DeepSeek API无响应\n";
}

echo "\n=== 测试完成 ===\n";

// 测试建议
echo "\n=== 建议 ===\n";
if ($qwenHttpCode === 200 && $deepseekHttpCode === 200) {
    echo "✅ 所有API连接正常，可以进行完整测试\n";
} else {
    echo "❌ 发现API连接问题，请检查：\n";
    echo "1. API密钥是否正确\n";
    echo "2. 网络连接是否正常\n";
    echo "3. API服务是否可用\n";
    echo "4. 账户余额是否充足\n";
}
?>
