<?php
// 调试 API 调用问题
echo "=== API 调用调试 ===\n\n";

// 1. 检查配置
echo "1. 检查配置...\n";
$config = require 'config.php';

echo "   Qwen API URL: " . $config['qwen']['api_url'] . "\n";
echo "   Qwen API Key: " . substr($config['qwen']['api_key'], 0, 10) . "...\n";
echo "   Qwen Model: " . $config['qwen']['model'] . "\n";
echo "   Response Format: " . $config['qwen']['response_format'] . "\n\n";

// 2. 测试图片 URL
echo "2. 测试图片 URL...\n";
$testImageUrl = "http://solve.igmdns.com/img/01.jpg";
echo "   测试 URL: $testImageUrl\n";

$headers = get_headers($testImageUrl, 1);
if ($headers && strpos($headers[0], '200') !== false) {
    echo "   ✅ 图片 URL 可访问\n";
    echo "   Content-Type: " . $headers['Content-Type'] . "\n";
    echo "   Content-Length: " . $headers['Content-Length'] . "\n";
} else {
    echo "   ❌ 图片 URL 无法访问\n";
    echo "   响应: " . print_r($headers, true) . "\n";
}

// 3. 构建 API 请求数据
echo "\n3. 构建 API 请求数据...\n";
$qwenConfig = $config['qwen'];

$requestData = [
    'model' => $qwenConfig['model'],
    'input' => [
        'messages' => [
            [
                'role' => $qwenConfig['role'],
                'content' => $qwenConfig['system_content']
            ],
            [
                'role' => 'user',
                'content' => [
                    ['image' => $testImageUrl],
                    ['text' => $qwenConfig['user_prompt_template']]
                ]
            ]
        ]
    ],
    'parameters' => [
        'temperature' => $qwenConfig['temperature'],
        'top_p' => $qwenConfig['top_p'],
        'top_k' => $qwenConfig['top_k'],
        'do_sample' => $qwenConfig['do_sample'],
        'response_format' => $qwenConfig['response_format'] === 'json'
            ? ['type' => 'json_object']
            : ['type' => 'text'],
        'detail' => $qwenConfig['detail']
    ]
];

echo "   请求数据构建完成\n";
echo "   模型: " . $requestData['model'] . "\n";
echo "   参数数量: " . count($requestData['parameters']) . "\n";

// 4. 测试 API 调用
echo "\n4. 测试 API 调用...\n";

$headers = [
    'Content-Type: application/json',
    'Authorization: Bearer ' . $qwenConfig['api_key']
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $qwenConfig['api_url']);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$startTime = microtime(true);
$response = curl_exec($ch);
$endTime = microtime(true);

$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

$responseTime = ($endTime - $startTime) * 1000;

echo "   HTTP 状态码: $httpCode\n";
echo "   响应时间: " . number_format($responseTime, 2) . "ms\n";

if ($curlError) {
    echo "   ❌ CURL 错误: $curlError\n";
} else {
    echo "   ✅ CURL 执行成功\n";
}

if ($response) {
    $responseData = json_decode($response, true);
    
    if ($responseData === null) {
        echo "   ❌ 响应不是有效的 JSON\n";
        echo "   原始响应前500字符: " . substr($response, 0, 500) . "\n";
    } else {
        echo "   ✅ 响应是有效的 JSON\n";
        
        if (isset($responseData['output'])) {
            echo "   ✅ 包含 output 字段\n";
            if (isset($responseData['output']['text'])) {
                echo "   ✅ 包含 text 内容\n";
                echo "   文本长度: " . strlen($responseData['output']['text']) . " 字符\n";
            } else {
                echo "   ❌ 缺少 text 内容\n";
            }
        } else {
            echo "   ❌ 缺少 output 字段\n";
        }
        
        if (isset($responseData['usage'])) {
            echo "   ✅ 包含 usage 信息\n";
            echo "   Token 使用: " . json_encode($responseData['usage']) . "\n";
        }
        
        if (isset($responseData['error'])) {
            echo "   ❌ API 返回错误: " . json_encode($responseData['error']) . "\n";
        }

        // 显示完整响应用于调试
        echo "   完整响应: " . json_encode($responseData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
    }
} else {
    echo "   ❌ 没有收到响应\n";
}

// 5. 检查网络连接
echo "\n5. 检查网络连接...\n";
$testUrls = [
    'https://www.baidu.com',
    'https://dashscope.aliyuncs.com',
    $qwenConfig['api_url']
];

foreach ($testUrls as $url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "   ❌ $url: $error\n";
    } else {
        echo "   ✅ $url: HTTP $httpCode\n";
    }
}

// 6. 检查 PHP 扩展
echo "\n6. 检查 PHP 扩展...\n";
$requiredExtensions = ['curl', 'json', 'openssl'];

foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        echo "   ✅ $ext 扩展已加载\n";
    } else {
        echo "   ❌ $ext 扩展未加载\n";
    }
}

echo "\n=== 调试完成 ===\n";
