<?php
// 测试 presence_penalty 参数
echo "=== 测试 presence_penalty 参数 ===\n\n";

// 1. 检查配置文件中的 presence_penalty 值
echo "1. 检查配置文件中的 presence_penalty 参数...\n";
$config = require 'config.php';

if (isset($config['deepseek']['presence_penalty'])) {
    $presencePenalty = $config['deepseek']['presence_penalty'];
    $type = gettype($presencePenalty);
    echo "   ✅ deepseek.presence_penalty: $presencePenalty ($type)\n";
} else {
    echo "   ❌ deepseek.presence_penalty: 缺失\n";
}

// 2. 检查 frequency_penalty 参数（对比）
if (isset($config['deepseek']['frequency_penalty'])) {
    $frequencyPenalty = $config['deepseek']['frequency_penalty'];
    $type = gettype($frequencyPenalty);
    echo "   ✅ deepseek.frequency_penalty: $frequencyPenalty ($type)\n";
} else {
    echo "   ❌ deepseek.frequency_penalty: 缺失\n";
}

// 3. 检查 API 调用中是否包含这些参数
echo "\n2. 检查 API 调用数据结构...\n";

// 模拟获取 DeepSeek 请求数据
function getDeepseekRequestDataTest($questionContent, $deepseekConfig) {
    $data = [
        'model' => $deepseekConfig['model'],
        'messages' => [
            [
                'role' => $deepseekConfig['role'],
                'content' => $deepseekConfig['system_content']
            ],
            [
                'role' => 'user',
                'content' => $questionContent . $deepseekConfig['user_prompt_template']
            ]
        ],
        'temperature' => $deepseekConfig['temperature'],
        'max_tokens' => $deepseekConfig['max_tokens'],
        'top_p' => $deepseekConfig['top_p'],
        'top_k' => $deepseekConfig['top_k'],
        'do_sample' => $deepseekConfig['do_sample'],
        'frequency_penalty' => $deepseekConfig['frequency_penalty'],
        'presence_penalty' => $deepseekConfig['presence_penalty']
    ];

    // 添加 response_format 参数（如果配置为 json）
    if ($deepseekConfig['response_format'] === 'json') {
        $data['response_format'] = ['type' => 'json_object'];
    }

    return $data;
}

$testQuestionContent = "测试题目内容";
$requestData = getDeepseekRequestDataTest($testQuestionContent, $config['deepseek']);

echo "   API 请求数据中的惩罚参数:\n";
echo "   • frequency_penalty: " . $requestData['frequency_penalty'] . "\n";
echo "   • presence_penalty: " . $requestData['presence_penalty'] . "\n";

// 4. 参数说明
echo "\n3. 参数说明:\n";
echo "   📋 frequency_penalty (频率惩罚):\n";
echo "   • 范围: -2.0 到 2.0\n";
echo "   • 作用: 降低模型重复相同词汇的可能性\n";
echo "   • 正值: 减少重复，负值: 增加重复\n";
echo "   • 当前值: " . $config['deepseek']['frequency_penalty'] . "\n\n";

echo "   📋 presence_penalty (存在惩罚):\n";
echo "   • 范围: -2.0 到 2.0\n";
echo "   • 作用: 降低模型谈论已出现话题的可能性\n";
echo "   • 正值: 鼓励新话题，负值: 专注当前话题\n";
echo "   • 当前值: " . $config['deepseek']['presence_penalty'] . "\n\n";

// 5. 测试不同的 presence_penalty 值
echo "4. 测试不同 presence_penalty 值的效果...\n";

$testValues = [
    ['value' => -1.0, 'description' => '负值：专注当前话题'],
    ['value' => 0.0, 'description' => '中性：默认行为'],
    ['value' => 1.0, 'description' => '正值：鼓励新话题']
];

foreach ($testValues as $test) {
    echo "   测试值: {$test['value']} ({$test['description']})\n";
    
    // 创建测试配置
    $testConfig = $config['deepseek'];
    $testConfig['presence_penalty'] = $test['value'];
    
    $testRequestData = getDeepseekRequestDataTest($testQuestionContent, $testConfig);
    
    echo "   • 请求数据中的 presence_penalty: " . $testRequestData['presence_penalty'] . "\n";
    echo "   • 数据类型: " . gettype($testRequestData['presence_penalty']) . "\n\n";
}

// 6. 配置管理器界面检查
echo "5. 配置管理器界面检查...\n";
echo "   访问 http://localhost:8080/config_manager.php\n";
echo "   在 DeepSeek-Chat 配置区域应该能看到:\n";
echo "   • 频率惩罚 (-2 to 2): [输入框]\n";
echo "   • 存在惩罚 (-2 to 2): [输入框]\n";
echo "   ✅ 界面控件已添加\n\n";

// 7. 实际 API 测试（可选）
echo "6. 实际 API 测试建议...\n";
echo "   可以通过以下方式测试 presence_penalty 效果:\n";
echo "   1. 设置不同的 presence_penalty 值 (-1.0, 0.0, 1.0)\n";
echo "   2. 使用相同的图片进行多次测试\n";
echo "   3. 观察 DeepSeek 回答的多样性变化\n";
echo "   4. 正值应该产生更多样化的回答\n";
echo "   5. 负值应该产生更专注的回答\n\n";

// 8. 最佳实践建议
echo "7. 最佳实践建议:\n";
echo "   📌 驾照考试场景推荐设置:\n";
echo "   • frequency_penalty: 0.0 (避免影响标准答案)\n";
echo "   • presence_penalty: 0.0 (保持专注于题目解析)\n";
echo "   \n";
echo "   📌 创意写作场景推荐设置:\n";
echo "   • frequency_penalty: 0.5-1.0 (减少重复)\n";
echo "   • presence_penalty: 0.5-1.0 (鼓励新话题)\n";
echo "   \n";
echo "   📌 专业分析场景推荐设置:\n";
echo "   • frequency_penalty: 0.0-0.3 (适度减少重复)\n";
echo "   • presence_penalty: -0.5-0.0 (专注当前话题)\n\n";

echo "=== 测试完成 ===\n";
echo "✅ presence_penalty 参数配置完整\n";
echo "✅ API 调用中正确包含该参数\n";
echo "✅ 配置管理器界面已添加控制\n";
echo "🎯 可以根据使用场景调整参数值\n";
?>
