<?php
// 测试Web响应格式
$config = require_once 'config.php';

// 模拟一个API调用
$testImageUrl = 'http://solve.igmdns.com/img/23.jpg';

echo "=== 测试Web响应格式 ===\n\n";

// 发送POST请求
$data = ['image_url' => $testImageUrl];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/index.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 120);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP状态码: $httpCode\n\n";

if ($response) {
    $data = json_decode($response, true);
    
    if ($data && $data['success']) {
        echo "✅ API调用成功！\n\n";
        
        echo "=== 🖼️ Qwen响应格式 ===\n";
        echo "类型: " . gettype($data['qwen_response']) . "\n";
        echo "长度: " . strlen($data['qwen_response']) . " 字符\n";
        echo "内容预览:\n";
        echo substr($data['qwen_response'], 0, 200) . "...\n\n";
        
        echo "=== 🧠 DeepSeek响应格式 ===\n";
        echo "类型: " . gettype($data['deepseek_response']) . "\n";
        echo "长度: " . strlen($data['deepseek_response']) . " 字符\n";
        echo "内容预览:\n";
        echo substr($data['deepseek_response'], 0, 200) . "...\n\n";
        
        echo "=== 📋 完整响应结构 ===\n";
        echo "响应字段: " . implode(', ', array_keys($data)) . "\n";
        echo "响应大小: " . strlen($response) . " 字节\n";
        
    } else {
        echo "❌ API调用失败\n";
        echo "错误: " . ($data['error'] ?? '未知错误') . "\n";
    }
} else {
    echo "❌ 无法获取响应\n";
}

echo "\n=== 测试完成 ===\n";
?>
