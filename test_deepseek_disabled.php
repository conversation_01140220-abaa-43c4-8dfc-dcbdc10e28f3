<?php
// 测试 DeepSeek 禁用状态
echo "=== 测试 DeepSeek 禁用状态 ===\n\n";

// 1. 临时修改配置文件，禁用 DeepSeek
echo "1. 临时禁用 DeepSeek...\n";

$configFile = 'config.php';
$configContent = file_get_contents($configFile);
$originalContent = $configContent;

// 将 enabled => true 改为 enabled => false
$modifiedContent = str_replace("'enabled' => true,", "'enabled' => false,", $configContent);
file_put_contents($configFile, $modifiedContent);

echo "   ✅ 已临时禁用 DeepSeek\n";

// 2. 测试 API 调用
echo "\n2. 测试 API 调用（DeepSeek 禁用状态）...\n";

$testImageUrl = "http://solve.igmdns.com/img/01.jpg";
$requestData = ['image_url' => $testImageUrl];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8080/index.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($requestData));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 60);

$startTime = microtime(true);
$response = curl_exec($ch);
$endTime = microtime(true);

$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

$responseTime = ($endTime - $startTime) * 1000;

echo "   HTTP 状态码: $httpCode\n";
echo "   响应时间: " . number_format($responseTime, 2) . "ms\n";

if ($httpCode === 200 && $response) {
    $responseData = json_decode($response, true);
    
    if ($responseData && isset($responseData['success']) && $responseData['success']) {
        echo "   ✅ API 调用成功\n";
        
        // 检查 Qwen 响应
        if (isset($responseData['qwen_response'])) {
            echo "   ✅ 包含 Qwen 响应\n";
            echo "   Qwen 内容长度: " . strlen($responseData['qwen_response']['content']) . " 字符\n";
            echo "   Qwen 响应时间: " . $responseData['qwen_response']['duration_ms'] . "ms\n";
        }
        
        // 检查 DeepSeek 响应
        if (isset($responseData['deepseek_response'])) {
            $deepseekResp = $responseData['deepseek_response'];
            
            if (isset($deepseekResp['status']) && $deepseekResp['status'] === 'disabled') {
                echo "   ✅ DeepSeek 正确显示为禁用状态\n";
                echo "   DeepSeek 状态: " . $deepseekResp['status'] . "\n";
                echo "   DeepSeek 内容: " . $deepseekResp['content'] . "\n";
                echo "   DeepSeek 响应时间: " . $deepseekResp['duration_ms'] . "ms\n";
                echo "   DeepSeek Token 使用: " . $deepseekResp['tokens']['total_tokens'] . "\n";
            } else {
                echo "   ❌ DeepSeek 应该显示为禁用状态\n";
                echo "   实际内容: " . $deepseekResp['content'] . "\n";
            }
        } else {
            echo "   ❌ 缺少 DeepSeek 响应字段\n";
        }
        
        // 显示完整响应（用于调试）
        echo "\n   完整响应:\n";
        echo "   " . json_encode($responseData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
        
    } else {
        echo "   ❌ API 调用失败\n";
        if (isset($responseData['error'])) {
            echo "   错误: " . $responseData['error'] . "\n";
        }
    }
} else {
    echo "   ❌ HTTP 错误: $httpCode\n";
    echo "   响应内容: $response\n";
}

// 3. 恢复原始配置
echo "\n3. 恢复原始配置...\n";
file_put_contents($configFile, $originalContent);
echo "   ✅ 配置已恢复\n";

// 4. 验证恢复
$restoredConfig = require $configFile;
if ($restoredConfig['deepseek']['enabled']) {
    echo "   ✅ DeepSeek 已重新启用\n";
} else {
    echo "   ⚠️  DeepSeek 仍处于禁用状态\n";
}

echo "\n=== 测试完成 ===\n";
echo "✅ DeepSeek 禁用功能测试完成\n";
echo "📋 建议通过配置管理器界面进行正式的开关操作\n";
