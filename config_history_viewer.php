<?php
// 配置历史查看器
header('Content-Type: text/html; charset=utf-8');

$action = $_GET['action'] ?? 'list';
$file = $_GET['file'] ?? '';

// 处理恢复配置
if ($action === 'restore' && $file) {
    $historyFile = 'config_history/' . basename($file);
    if (file_exists($historyFile)) {
        $historyData = json_decode(file_get_contents($historyFile), true);
        if ($historyData && isset($historyData['full_config'])) {
            $configContent = "<?php\n// API配置文件\n// 请在此处填入您的API密钥和调整技术参数\n\nreturn " . var_export($historyData['full_config'], true) . ";\n";
            file_put_contents('config.php', $configContent);
            $message = "✅ 配置已恢复到 " . $historyData['timestamp'];
        }
    }
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置历史记录</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #333; text-align: center; }
        .nav {
            margin-bottom: 20px;
            text-align: center;
        }
        .nav a {
            display: inline-block;
            padding: 8px 15px;
            margin: 0 5px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 3px;
        }
        .nav a:hover { background-color: #0056b3; }
        .history-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 15px;
            padding: 15px;
            background-color: #f9f9f9;
        }
        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .timestamp {
            font-weight: bold;
            color: #333;
        }
        .change-count {
            background-color: #007bff;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .changes {
            margin-top: 10px;
        }
        .change-item {
            margin: 5px 0;
            padding: 8px;
            background-color: white;
            border-radius: 3px;
            border-left: 3px solid #007bff;
        }
        .change-key {
            font-weight: bold;
            color: #333;
        }
        .change-value {
            margin-left: 10px;
            font-family: monospace;
            font-size: 12px;
        }
        .old-value {
            color: #dc3545;
            text-decoration: line-through;
        }
        .new-value {
            color: #28a745;
        }
        .actions {
            margin-top: 10px;
        }
        .btn {
            padding: 5px 10px;
            margin-right: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            font-size: 12px;
        }
        .btn-restore {
            background-color: #28a745;
            color: white;
        }
        .btn-view {
            background-color: #17a2b8;
            color: white;
        }
        .message {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .detail-view {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        .config-json {
            background-color: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📜 配置历史记录</h1>
        
        <div class="nav">
            <a href="config_manager.php">⚙️ 配置管理</a>
            <a href="?action=list">📋 历史列表</a>
            <a href="test.html">🧪 测试API</a>
        </div>
        
        <?php if (isset($message)): ?>
            <div class="message"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <?php if ($action === 'list'): ?>
            <h2>配置变更历史</h2>
            
            <?php
            $indexFile = 'config_history/index.json';
            if (file_exists($indexFile)) {
                $index = json_decode(file_get_contents($indexFile), true) ?: [];
                $index = array_reverse($index); // 最新的在前面
                
                if (empty($index)) {
                    echo "<p>暂无配置变更记录。</p>";
                } else {
                    foreach ($index as $record) {
                        $historyFile = 'config_history/' . $record['file'];
                        if (file_exists($historyFile)) {
                            $historyData = json_decode(file_get_contents($historyFile), true);
                            ?>
                            <div class="history-item">
                                <div class="history-header">
                                    <span class="timestamp">🕒 <?php echo $record['timestamp']; ?></span>
                                    <span class="change-count"><?php echo $record['change_count']; ?> 项变更</span>
                                </div>
                                
                                <?php if (!empty($historyData['changes'])): ?>
                                    <div class="changes">
                                        <?php foreach (array_slice($historyData['changes'], 0, 5) as $key => $change): ?>
                                            <div class="change-item">
                                                <div class="change-key"><?php echo htmlspecialchars($key); ?></div>
                                                <div class="change-value">
                                                    <span class="old-value"><?php echo htmlspecialchars(json_encode($change['old'])); ?></span>
                                                    → 
                                                    <span class="new-value"><?php echo htmlspecialchars(json_encode($change['new'])); ?></span>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                        
                                        <?php if (count($historyData['changes']) > 5): ?>
                                            <div style="text-align: center; margin-top: 10px; color: #666;">
                                                还有 <?php echo count($historyData['changes']) - 5; ?> 项变更...
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="actions">
                                    <a href="?action=view&file=<?php echo urlencode($record['file']); ?>" class="btn btn-view">👁️ 查看详情</a>
                                    <a href="?action=restore&file=<?php echo urlencode($record['file']); ?>" 
                                       class="btn btn-restore" 
                                       onclick="return confirm('确定要恢复到此配置吗？当前配置将被覆盖。')">🔄 恢复配置</a>
                                </div>
                            </div>
                            <?php
                        }
                    }
                }
            } else {
                echo "<p>暂无配置变更记录。</p>";
            }
            ?>
            
        <?php elseif ($action === 'view' && $file): ?>
            <?php
            $historyFile = 'config_history/' . basename($file);
            if (file_exists($historyFile)) {
                $historyData = json_decode(file_get_contents($historyFile), true);
                ?>
                <h2>配置详情 - <?php echo $historyData['timestamp']; ?></h2>
                
                <div class="detail-view">
                    <h3>📊 变更统计</h3>
                    <p>变更时间: <?php echo $historyData['timestamp']; ?></p>
                    <p>变更项目: <?php echo count($historyData['changes']); ?> 项</p>
                    
                    <?php if (!empty($historyData['changes'])): ?>
                        <h3>📝 详细变更</h3>
                        <?php foreach ($historyData['changes'] as $key => $change): ?>
                            <div class="change-item">
                                <div class="change-key"><?php echo htmlspecialchars($key); ?></div>
                                <div class="change-value">
                                    <div><strong>旧值:</strong> <span class="old-value"><?php echo htmlspecialchars(json_encode($change['old'], JSON_UNESCAPED_UNICODE)); ?></span></div>
                                    <div><strong>新值:</strong> <span class="new-value"><?php echo htmlspecialchars(json_encode($change['new'], JSON_UNESCAPED_UNICODE)); ?></span></div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                    
                    <h3>⚙️ 完整配置</h3>
                    <div class="config-json"><?php echo htmlspecialchars(json_encode($historyData['full_config'], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT)); ?></div>
                    
                    <div class="actions" style="margin-top: 20px;">
                        <a href="?action=list" class="btn btn-view">← 返回列表</a>
                        <a href="?action=restore&file=<?php echo urlencode($file); ?>" 
                           class="btn btn-restore" 
                           onclick="return confirm('确定要恢复到此配置吗？当前配置将被覆盖。')">🔄 恢复此配置</a>
                    </div>
                </div>
                <?php
            } else {
                echo "<p>配置文件不存在。</p>";
            }
            ?>
        <?php endif; ?>
    </div>
</body>
</html>
