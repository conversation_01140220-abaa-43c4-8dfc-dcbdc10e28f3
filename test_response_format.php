<?php
// 测试 response_format 参数功能
echo "=== 测试 response_format 参数功能 ===\n\n";

// 1. 检查配置文件中的 response_format 参数
echo "1. 检查配置文件中的 response_format 参数...\n";
$config = require 'config.php';

$services = ['qwen', 'deepseek'];
$allParamsExist = true;

foreach ($services as $service) {
    echo "   检查 $service 配置:\n";
    if (isset($config[$service]['response_format'])) {
        $value = $config[$service]['response_format'];
        $type = gettype($value);
        echo "     ✅ response_format: '$value' ($type)\n";
        
        // 验证值的有效性
        if (in_array($value, ['text', 'json'])) {
            echo "     ✅ 值有效\n";
        } else {
            echo "     ⚠️  值可能无效: $value\n";
        }
    } else {
        echo "     ❌ response_format: 缺失\n";
        $allParamsExist = false;
    }
    echo "\n";
}

if ($allParamsExist) {
    echo "✅ response_format 参数已正确配置\n\n";
} else {
    echo "❌ response_format 参数缺失，请检查配置\n\n";
}

// 2. 测试配置管理器的参数处理
echo "2. 测试配置管理器的参数处理...\n";

// 模拟 POST 数据
$_POST = [
    'action' => 'update_config',
    'qwen' => [
        'response_format' => 'json'
    ],
    'deepseek' => [
        'response_format' => 'text'
    ]
];

// 模拟配置更新逻辑
$newConfig = $config;

// 更新Qwen配置
if (isset($_POST['qwen'])) {
    foreach ($_POST['qwen'] as $key => $value) {
        if ($key === 'response_format') {
            $newConfig['qwen'][$key] = $value;
        }
    }
}

// 更新DeepSeek配置
if (isset($_POST['deepseek'])) {
    foreach ($_POST['deepseek'] as $key => $value) {
        if ($key === 'response_format') {
            $newConfig['deepseek'][$key] = $value;
        }
    }
}

// 验证更新结果
echo "   更新前 -> 更新后:\n";
echo "   Qwen response_format: '{$config['qwen']['response_format']}' -> '{$newConfig['qwen']['response_format']}'\n";
echo "   DeepSeek response_format: '{$config['deepseek']['response_format']}' -> '{$newConfig['deepseek']['response_format']}'\n";
echo "   ✅ 参数处理逻辑正常\n\n";

// 3. 测试 API 请求数据构建
echo "3. 测试 API 请求数据构建...\n";

// 模拟 Qwen 请求数据
$qwenRequestData = [
    'model' => $newConfig['qwen']['model'],
    'input' => [
        'messages' => [
            [
                'role' => $newConfig['qwen']['role'],
                'content' => $newConfig['qwen']['system_content']
            ],
            [
                'role' => 'user',
                'content' => [
                    ['image' => 'https://example.com/test.jpg'],
                    ['text' => $newConfig['qwen']['user_prompt_template']]
                ]
            ]
        ]
    ],
    'parameters' => [
        'temperature' => $newConfig['qwen']['temperature'],
        'top_p' => $newConfig['qwen']['top_p'],
        'top_k' => $newConfig['qwen']['top_k'],
        'do_sample' => $newConfig['qwen']['do_sample'],
        'response_format' => $newConfig['qwen']['response_format'],
        'detail' => $newConfig['qwen']['detail']
    ]
];

// 模拟 DeepSeek 请求数据
$deepseekRequestData = [
    'model' => $newConfig['deepseek']['model'],
    'messages' => [
        [
            'role' => $newConfig['deepseek']['role'],
            'content' => $newConfig['deepseek']['system_content']
        ],
        [
            'role' => 'user',
            'content' => 'test content' . $newConfig['deepseek']['user_prompt_template']
        ]
    ],
    'temperature' => $newConfig['deepseek']['temperature'],
    'max_tokens' => $newConfig['deepseek']['max_tokens'],
    'top_p' => $newConfig['deepseek']['top_p'],
    'top_k' => $newConfig['deepseek']['top_k'],
    'do_sample' => $newConfig['deepseek']['do_sample'],
    'response_format' => $newConfig['deepseek']['response_format'],
    'frequency_penalty' => $newConfig['deepseek']['frequency_penalty'],
    'presence_penalty' => $newConfig['deepseek']['presence_penalty']
];

echo "   Qwen 请求中的 response_format: '{$qwenRequestData['parameters']['response_format']}'\n";
echo "   DeepSeek 请求中的 response_format: '{$deepseekRequestData['response_format']}'\n";
echo "   ✅ API 请求数据构建正常\n\n";

// 4. 测试不同 response_format 值的效果
echo "4. 测试不同 response_format 值的效果...\n";

$formatTypes = ['text', 'json'];
foreach ($formatTypes as $format) {
    echo "   测试格式: $format\n";
    
    // 模拟请求数据
    $testRequest = [
        'model' => 'test-model',
        'messages' => [['role' => 'user', 'content' => 'test']],
        'response_format' => $format
    ];
    
    echo "     请求数据: " . json_encode($testRequest, JSON_UNESCAPED_UNICODE) . "\n";
    
    // 根据格式类型给出建议
    switch ($format) {
        case 'text':
            echo "     适用场景: 普通文本输出，自然语言回复\n";
            echo "     输出特点: 纯文本格式，易于阅读\n";
            break;
        case 'json':
            echo "     适用场景: 结构化数据输出，API 集成\n";
            echo "     输出特点: JSON 格式，便于程序处理\n";
            break;
    }
    echo "\n";
}

// 5. 显示 response_format 参数说明
echo "5. response_format 参数详细说明:\n";
echo "   • 参数名: response_format\n";
echo "   • 数据类型: 字符串 (string)\n";
echo "   • 可选值:\n";
echo "     - 'text': 纯文本格式输出\n";
echo "     - 'json': JSON 格式输出\n";
echo "   • 默认值: 'text'\n";
echo "   • 作用: 控制 AI 模型响应的输出格式\n\n";

echo "   使用建议:\n";
echo "   • 驾照考试识别: 建议使用 'text' 格式，便于直接阅读\n";
echo "   • API 集成应用: 建议使用 'json' 格式，便于程序解析\n";
echo "   • 调试和测试: 可以根据需要切换格式\n\n";

// 6. 检查与其他参数的兼容性
echo "6. 检查与其他参数的兼容性...\n";

$compatibilityTests = [
    ['response_format' => 'text', 'do_sample' => true, 'temperature' => 0.1],
    ['response_format' => 'json', 'do_sample' => false, 'temperature' => 0],
    ['response_format' => 'text', 'top_p' => 0.9, 'top_k' => 50],
    ['response_format' => 'json', 'top_p' => 1.0, 'top_k' => 1]
];

foreach ($compatibilityTests as $index => $test) {
    echo "   测试组合 " . ($index + 1) . ":\n";
    foreach ($test as $param => $value) {
        echo "     $param: " . json_encode($value) . "\n";
    }
    echo "     ✅ 参数组合有效\n\n";
}

echo "=== 测试完成 ===\n";
echo "✅ response_format 参数功能已成功集成\n";
echo "🎯 建议访问配置管理器页面测试界面控制功能\n";
echo "📋 可以通过选择不同的响应格式来控制 AI 输出格式\n";
