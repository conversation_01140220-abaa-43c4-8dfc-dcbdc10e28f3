<?php
// 调试配置历史记录功能
echo "=== 配置历史记录调试 ===\n\n";

// 1. 检查配置历史目录
echo "1. 检查配置历史目录...\n";
$historyDir = 'config_history';
if (is_dir($historyDir)) {
    echo "   ✅ 目录存在: $historyDir\n";
    echo "   权限: " . substr(sprintf('%o', fileperms($historyDir)), -4) . "\n";
    
    // 列出所有文件
    $files = glob($historyDir . '/*.json');
    echo "   文件数量: " . count($files) . "\n";
    
    if (count($files) > 0) {
        echo "   最新文件: " . basename(end($files)) . "\n";
    }
} else {
    echo "   ❌ 目录不存在: $historyDir\n";
}

// 2. 检查索引文件
echo "\n2. 检查索引文件...\n";
$indexFile = $historyDir . '/index.json';
if (file_exists($indexFile)) {
    echo "   ✅ 索引文件存在\n";
    $indexContent = file_get_contents($indexFile);
    $index = json_decode($indexContent, true);
    
    if ($index === null) {
        echo "   ❌ 索引文件JSON格式错误: " . json_last_error_msg() . "\n";
        echo "   文件内容前100字符: " . substr($indexContent, 0, 100) . "\n";
    } else {
        echo "   ✅ 索引文件格式正确\n";
        echo "   记录数量: " . count($index) . "\n";
        
        // 检查最近几条记录
        $recent = array_slice($index, -3);
        echo "   最近3条记录:\n";
        foreach ($recent as $record) {
            echo "     - {$record['timestamp']}: {$record['change_count']} 项变更\n";
        }
    }
} else {
    echo "   ❌ 索引文件不存在\n";
}

// 3. 检查具体的历史文件
echo "\n3. 检查历史文件内容...\n";
$files = glob($historyDir . '/config_*.json'); // 只获取配置文件，排除index.json
if (count($files) > 0) {
    // 检查最新的文件
    $latestFile = end($files);
    echo "   检查文件: " . basename($latestFile) . "\n";
    
    $content = file_get_contents($latestFile);
    $data = json_decode($content, true);
    
    if ($data === null) {
        echo "   ❌ 文件JSON格式错误: " . json_last_error_msg() . "\n";
    } else {
        echo "   ✅ 文件格式正确\n";
        echo "   时间戳: " . $data['timestamp'] . "\n";
        echo "   变更数量: " . $data['change_count'] . "\n";
        echo "   实际变更项: " . count($data['changes']) . "\n";
        
        if ($data['change_count'] != count($data['changes'])) {
            echo "   ⚠️  变更数量不匹配!\n";
        }
        
        // 显示变更详情
        if (!empty($data['changes'])) {
            echo "   变更详情:\n";
            foreach ($data['changes'] as $key => $change) {
                echo "     - $key: " . json_encode($change['old']) . " → " . json_encode($change['new']) . "\n";
            }
        }
    }
}

// 4. 测试配置比较功能
echo "\n4. 测试配置比较功能...\n";

// 加载配置管理器函数
require_once 'config_manager.php';

// 创建测试配置
$oldConfig = [
    'qwen' => ['temperature' => 0, 'model' => 'qwen-vl-plus'],
    'deepseek' => ['temperature' => 0, 'model' => 'deepseek-chat'],
    'system' => ['debug_mode' => false]
];

$newConfig = [
    'qwen' => ['temperature' => 0.1, 'model' => 'qwen-vl-plus'],
    'deepseek' => ['temperature' => 0.2, 'model' => 'deepseek-chat'],
    'system' => ['debug_mode' => true]
];

echo "   测试扁平化函数...\n";
$oldFlat = flattenArray($oldConfig);
$newFlat = flattenArray($newConfig);

echo "   旧配置扁平化: " . count($oldFlat) . " 项\n";
echo "   新配置扁平化: " . count($newFlat) . " 项\n";

// 手动计算变更
$changes = [];
$allKeys = array_unique(array_merge(array_keys($oldFlat), array_keys($newFlat)));

foreach ($allKeys as $key) {
    $oldValue = $oldFlat[$key] ?? null;
    $newValue = $newFlat[$key] ?? null;
    
    if ($oldValue !== $newValue) {
        $changes[$key] = [
            'old' => $oldValue,
            'new' => $newValue
        ];
    }
}

echo "   检测到变更: " . count($changes) . " 项\n";
foreach ($changes as $key => $change) {
    echo "     - $key: " . json_encode($change['old']) . " → " . json_encode($change['new']) . "\n";
}

// 5. 检查权限问题
echo "\n5. 检查文件权限...\n";
if (is_writable($historyDir)) {
    echo "   ✅ 历史目录可写\n";
} else {
    echo "   ❌ 历史目录不可写\n";
}

if (file_exists($indexFile)) {
    if (is_writable($indexFile)) {
        echo "   ✅ 索引文件可写\n";
    } else {
        echo "   ❌ 索引文件不可写\n";
    }
}

// 6. 检查磁盘空间
echo "\n6. 检查磁盘空间...\n";
$freeBytes = disk_free_space('.');
$totalBytes = disk_total_space('.');
$usedPercent = (($totalBytes - $freeBytes) / $totalBytes) * 100;

echo "   可用空间: " . formatBytes($freeBytes) . "\n";
echo "   使用率: " . number_format($usedPercent, 1) . "%\n";

if ($freeBytes < 10 * 1024 * 1024) { // 小于10MB
    echo "   ⚠️  磁盘空间不足\n";
}

function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

echo "\n=== 调试完成 ===\n";
