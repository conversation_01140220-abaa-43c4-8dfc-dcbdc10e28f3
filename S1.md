将qwen返回的数据进行格式化处理，下面是几个样本，能发现question_text的值有一些区别，我需要实现的解析规则是：

1. 针对question_text字段的通用规则。如果question_text字段最前面的20个字节中如果出现了数字+、的序号，则将序号前的所有内容移除。

2. 当question_type为判断题时，需要把 "Y: 正确": "", "N: 错误": "" 处理成 "Y": "正确", "N": "错误",

3. 你可以尝试分析一下样本，给我更好的建议与我沟通。

4. 这样做的目的，因为这个json是视觉模型识图后返回的。为了后续的复用，我需要将他们存入缓存键，但因为模型的不一致性，导致在返回question_text值时出现了非预期的结果，所以需要将内容按照特有的格式进行解析处理。这样会增加后续的缓存命中。




{
  "question_type": "多选题",
  "question_text": "(多选题)12、唐某驾驶一辆大客车，乘载74人（核载55人），以每小时38公里的速度，行至一连续下陡坡转弯路段时，机动车翻入路侧溪水内，造成17人死亡、57人受伤。唐某的主要违法行为是什么？",
  "options": {
    "A": "超速行驶",
    "B": "客车超员",
    "C": "酒后驾驶",
    "D": "疲劳驾驶"
  }
}

{
  "question_type": "判断题",
  "question_text": "(判断题)8、驾驶机动车遇到同车道行驶的执行紧急任务的特种车辆时不得超车。",
  "options": {
    "Y: 正确": "",
    "N: 错误": ""
  }
}

{
  "question_type": "判断题",
  "question_text": "8、驾驶机动车遇到同车道行驶的执行紧急任务的特种车辆时不得超车。",
  "options": {
    "Y: 正确": "",
    "N: 错误": ""
  }
}

{
  "question_type": "判断题",
  "question_text": "驾驶机动车遇到同车道行驶的执行紧急任务的特种车辆时不得超车。",
  "options": {
    "Y: 正确": "",
    "N: 错误": ""
  }
}